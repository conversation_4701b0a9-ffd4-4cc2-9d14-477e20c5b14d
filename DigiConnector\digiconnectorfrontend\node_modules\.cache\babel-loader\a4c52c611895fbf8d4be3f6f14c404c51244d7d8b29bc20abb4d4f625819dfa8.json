{"ast": null, "code": "var _jsxFileName = \"E:\\\\digispin doc\\\\repos\\\\logistics\\\\DigiConnector\\\\digiconnectorfrontend\\\\src\\\\pages\\\\Product\\\\ProductList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { getExportProducts } from '../../api/products';\nimport { read, utils, writeFile } from \"xlsx\";\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faFileImport, faDownload } from '@fortawesome/free-solid-svg-icons';\nimport { Button, Toolbar, TextField, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Paper, Grid, TableSortLabel, Tabs, Tab, InputAdornment, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, DialogContentText } from '@mui/material';\nimport { SaveAlt as SaveAltIcon, Search as SearchIcon, Refresh as RefreshIcon, CloudUpload as CloudUploadIcon, Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { useNavigate } from \"react-router-dom\";\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const ProductList = ({\n  products\n}) => {\n  _s();\n  const fileInputRef = useRef(null);\n  const navigate = useNavigate();\n  const [searchText, setSearchText] = useState('');\n  const [selectedProducts, setSelectedProducts] = useState([]);\n  const [orderBy, setOrderBy] = useState(null);\n  const [order, setOrder] = useState('asc');\n  const [tabValue, setTabValue] = useState('All'); // Default to 'All' tab\n  const [channelDetails, setChannelDetails] = useState([]);\n  const [isChannelPublishedDialogOpen, setIsChannelPublishedDialogOpen] = useState(false);\n  const [isChannelUnpublishedDialogOpen, setIsChannelUnpublishedDialogOpen] = useState(false);\n  const [showImportDialog, setShowImportDialog] = useState(false);\n  const [checkedNotes, setCheckedNotes] = useState(false);\n  const [importButtonDisabled, setImportButtonDisabled] = useState(true);\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\n  const [deleteSku, setDeleteSku] = useState('');\n  const tabs = [\"All\", \"Active\", \"Archived\", \"Draft\", \"Published\", \"Unpublished\"];\n  const handleRowClick = product => {\n    navigate(`/products/edit/${product.sku}`);\n  };\n  const handleRefresh = () => {\n    window.location.reload();\n  };\n  const handleFileChange = () => {\n    if (fileInputRef.current && fileInputRef.current.files.length > 0) {\n      setImportButtonDisabled(false);\n    } else {\n      setImportButtonDisabled(true);\n    }\n  };\n  const hideUploadDialog = () => {\n    setShowImportDialog(false);\n  };\n\n  // Function to push products to the server\n  const pushProducts = async rows => {\n    try {\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n      await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product_varient_in_bulk?fby_user_id=${storedGroupCode}`, rows, {\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n        }\n      });\n    } catch (err) {\n      console.log(err.message);\n    }\n  };\n  const handleBulkImport = () => {\n    if (!importButtonDisabled) {\n      const file = fileInputRef.current.files[0];\n      // if (file) {\n      //     onFileChange(file);\n      //     setShowImportDialog(false);\n      // }\n      // const file = event.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = async event => {\n          const wb = read(event.target.result);\n          const sheets = wb.SheetNames;\n          if (sheets.length) {\n            const rows = utils.sheet_to_json(wb.Sheets[sheets[0]]);\n            await pushProducts(rows);\n          }\n        };\n        reader.readAsArrayBuffer(file);\n      }\n    }\n  };\n  const exportSampleFileDownload = async () => {\n    const wb = utils.book_new();\n    const ws = utils.json_to_sheet([]);\n    let headings = [[\"id\", \"title\", \"description\", \"option_1_name\", \"option_1_value\", \"option_2_name\", \"option_2_value\", \"sku\", \"gtin\", \"asin\", \"quantity\", \"price\", \"image_link\", \"brand\", \"tags\", \"category\", \"weight\", \"weight_unit\", \"height\", \"width\", \"length\", \"dimensions_units\"]];\n    let data = [];\n    utils.sheet_add_aoa(ws, headings);\n    utils.sheet_add_json(ws, data, {\n      origin: 'A2',\n      skipHeader: true\n    });\n    utils.book_append_sheet(wb, ws, 'Report');\n    writeFile(wb, `productSample.csv`);\n  };\n  const handleExport = async () => {\n    const wb = utils.book_new();\n    const ws = utils.json_to_sheet([]);\n    let headings = [[\"id\", \"title\", \"description\", \"option_1_name\", \"option_1_value\", \"option_2_name\", \"option_2_value\", \"sku\", \"gtin\", \"asin\", \"quantity\", \"price\", \"image_link\", \"brand\", \"tags\", \"category\", \"weight\", \"weight_unit\", \"height\", \"width\", \"length\", \"dimensions_units\"]];\n    let data = [];\n    try {\n      utils.sheet_add_aoa(ws, headings);\n      data = await getExportProducts();\n    } catch (error) {\n      data = [];\n      console.error('Error fetching products:', error);\n    }\n    utils.sheet_add_json(ws, data, {\n      origin: 'A2',\n      skipHeader: true\n    });\n    utils.book_append_sheet(wb, ws, 'Report');\n    writeFile(wb, `product.csv`);\n  };\n  const handleImport = () => {\n    setShowImportDialog(true);\n  };\n  const handleAddProduct = () => {\n    navigate('/products/edit');\n  };\n  const openDeleteDialog = () => {\n    setIsDeleteDialogOpen(true);\n  };\n  const handleDeleteSkuChange = event => {\n    setDeleteSku(event.target.value);\n  };\n\n  // Function to close the delete confirmation dialog\n  const closeDeleteDialog = () => {\n    setIsDeleteDialogOpen(false);\n  };\n  const handleChannelClick = async row => {\n    if (row.channel_status == 'published') {\n      setIsChannelPublishedDialogOpen(true);\n    } else {\n      setIsChannelUnpublishedDialogOpen(true);\n    }\n    try {\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n      const res = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_product?fby_user_id=${storedGroupCode}&sku=${row.sku}&isCreated=true`, {}, {\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: process.env.REACT_APP_ACCESS_TOKEN\n        }\n      });\n      if (res.data.length > 0) {\n        setChannelDetails(res.data);\n        setIsChannelPublishedDialogOpen(true);\n      } else {\n        setChannelDetails([]);\n        setIsChannelUnpublishedDialogOpen(true);\n      }\n    } catch (error) {\n      console.error('Error fetching channel details:', error);\n      setChannelDetails([]);\n      setIsChannelUnpublishedDialogOpen(true);\n    }\n  };\n  const handleAddTab = () => {\n    console.log(\"Add new tab functionality goes here\");\n  };\n  const createSortHandler = property => () => {\n    const isAsc = orderBy === property && order === 'asc';\n    setOrder(isAsc ? 'desc' : 'asc');\n    setOrderBy(property);\n  };\n  const filteredProducts = products.filter(product => {\n    const searchFields = ['title', 'status', 'sku', 'inventory_quantity', 'price', 'channel', 'created_at'];\n    return searchFields.some(field => product[field] && product[field].toString().toLowerCase().includes(searchText.toLowerCase()));\n  });\n  const sortedProducts = [...filteredProducts].sort((a, b) => {\n    if (orderBy) {\n      const comparison = a[orderBy].localeCompare(b[orderBy]);\n      return order === 'asc' ? comparison : -comparison;\n    }\n    return 0;\n  });\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return '#b9e5a4';\n      // Slightly darker green color for active status\n      case 'archived':\n        return '#e0e0e0';\n      // Slightly darker gray color for archived status\n      case 'draft':\n        return '#ffcc80';\n      // Slightly darker orange color for draft status\n      default:\n        return 'white';\n      // Default light color for other statuses\n    }\n  };\n  const getChannelStatusColor = status => {\n    switch (status) {\n      case 'published':\n        return '#b9e5a4';\n      default:\n        return '#e0e0e0';\n    }\n  };\n  const renderProductsByStatus = status => {\n    let filteredProduct = sortedProducts;\n    if (status !== 'All') {\n      if (status === 'Published') {\n        filteredProduct = filteredProduct.filter(product => product.channel_status === 'published');\n      } else if (status === 'Unpublished') {\n        filteredProduct = filteredProduct.filter(product => product.channel_status !== 'published');\n      } else {\n        filteredProduct = filteredProduct.filter(product => product.status === status.toLowerCase());\n      }\n    }\n    return filteredProduct;\n  };\n  const formatDate = dateString => {\n    if (!dateString) {\n      return \"\";\n    }\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0'); // Adding 1 because months are zero-indexed\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  };\n  const getQuantityString = row => {\n    // Use inventory_quantity if available, otherwise fall back to quantity\n    const displayQuantity = row.inventory_quantity !== undefined ? row.inventory_quantity : row.quantity || 0;\n    if (row.count == 0) {\n      return displayQuantity;\n    } else {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [displayQuantity, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 38\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#aaa'\n          },\n          children: [' ' + row.count || '', \" variants\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 17\n      }, this);\n    }\n  };\n  const handleDelete = async () => {\n    closeDeleteDialog(); // Close the delete confirmation dialog\n\n    try {\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n      await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/delete_shopify_product?fby_user_id=${storedGroupCode}&sku=${deleteSku}`, {}, {\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\n        }\n      });\n      // After successful deletion, you might want to refresh the product list\n      handleRefresh();\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      // Handle error, display an error message, or take appropriate action\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Toolbar, {\n          children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n            children: /*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"Product List\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginLeft: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 44\n              }, this),\n              onClick: handleAddProduct,\n              style: {\n                backgroundColor: '#1976d2',\n                color: '#fff',\n                marginLeft: '8px'\n              },\n              children: \"Add Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 44\n              }, this),\n              onClick: handleRefresh,\n              style: {\n                backgroundColor: '#2196f3',\n                marginLeft: '8px'\n              },\n              children: \"Refresh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(SaveAltIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 44\n              }, this),\n              onClick: handleExport,\n              style: {\n                backgroundColor: '#4caf50',\n                marginLeft: '8px'\n              },\n              children: \"Export\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 44\n              }, this),\n              onClick: handleImport,\n              style: {\n                backgroundColor: '#f44336',\n                marginLeft: '8px'\n              },\n              children: \"Import\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 44\n              }, this),\n              onClick: openDeleteDialog,\n              style: {\n                backgroundColor: '#f44336',\n                marginLeft: '8px'\n              },\n              children: \"Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginLeft: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            variant: \"outlined\",\n            placeholder: \"Search...\",\n            onChange: e => setSearchText(e.target.value),\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 37\n              }, this),\n              style: {\n                borderRadius: '2px'\n              }\n            },\n            sx: {\n              width: 300\n            } // Adjust the width as needed\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n            value: tabValue,\n            onChange: (e, newValue) => setTabValue(newValue),\n            \"aria-label\": \"product-tabs\",\n            variant: \"scrollable\",\n            scrollButtons: \"auto\",\n            children: [tabs.map(tab => /*#__PURE__*/_jsxDEV(Tab, {\n              label: tab,\n              value: tab\n            }, tab, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 33\n            }, this)), /*#__PURE__*/_jsxDEV(Tab, {\n              icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 39\n              }, this),\n              \"aria-label\": \"add-tab\",\n              onClick: handleAddTab\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          sx: {\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              sx: {\n                background: '#f5f5f5'\n              },\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(TableSortLabel, {\n                    active: orderBy === 'title',\n                    direction: orderBy === 'title' ? order : 'asc',\n                    onClick: createSortHandler('title'),\n                    children: \"Product\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Product Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(TableSortLabel, {\n                    active: orderBy === 'sku',\n                    direction: orderBy === 'sku' ? order : 'asc',\n                    onClick: createSortHandler('sku'),\n                    children: \"SKU\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Quantity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Channel Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Updated\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: renderProductsByStatus(tabValue).map(product => /*#__PURE__*/_jsxDEV(TableRow, {\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: selectedProducts.includes(product),\n                    onChange: () => {\n                      if (selectedProducts.includes(product)) {\n                        setSelectedProducts(selectedProducts.filter(item => item !== product));\n                      } else {\n                        setSelectedProducts([...selectedProducts, product]);\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"product-title\",\n                    onClick: () => handleRowClick(product),\n                    style: {\n                      cursor: 'pointer',\n                      color: '#1976d2',\n                      // Blue color\n                      transition: 'color 0.3s'\n                    },\n                    onMouseEnter: e => e.target.style.color = '#115293' // Darker blue when hovered\n                    ,\n                    onMouseLeave: e => e.target.style.color = '#1976d2' // Return to original color when not hovered\n                    ,\n                    children: product.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      backgroundColor: getStatusColor(product.status),\n                      borderRadius: '4px',\n                      padding: '4px'\n                    },\n                    children: product.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: product.sku\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: getQuantityString(product)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: product.variantPrice ? product.variantPrice : product.price\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      backgroundColor: getChannelStatusColor(product.channel_status),\n                      borderRadius: '4px',\n                      padding: '4px',\n                      cursor: 'pointer'\n                    },\n                    onClick: handleChannelClick,\n                    children: product.channel_status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatDate(product.updated_at)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 41\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 37\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: isChannelPublishedDialogOpen,\n        onClose: () => setIsChannelPublishedDialogOpen(false),\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Published Channel Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: 'Display Published channel details here'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setIsChannelPublishedDialogOpen(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: isChannelUnpublishedDialogOpen,\n        onClose: () => setIsChannelUnpublishedDialogOpen(false),\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Unpublished Channel Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: 'Display Unpublished channel details here'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setIsChannelUnpublishedDialogOpen(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showImportDialog,\n      onHide: hideUploadDialog,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-dialog-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Import Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-options\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"local-upload\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              class: \"drag-drop-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"file-upload\",\n                type: \"file\",\n                class: \"file-upload-input\",\n                ref: fileInputRef,\n                onChange: handleFileChange,\n                accept: \".csv\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                for: \"file-upload\",\n                class: \"file-upload-label\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  class: \"plus-icon\",\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  class: \"drag-drop-text\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faFileImport,\n                    className: \"upload-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 41\n                  }, this), \"Upload a CSV file or Drag & Drop it\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"template-download\",\n            onClick: exportSampleFileDownload,\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"download-template\",\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faDownload,\n                className: \"download-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 33\n              }, this), \"Download Template\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"import-notes\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"show-notes\",\n            checked: checkedNotes,\n            onChange: () => setCheckedNotes(!checkedNotes)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Prior to importing your products, please ensure that you have assigned a unique SKU code to each product or variant. Digihub will link products with matching SKU codes. The duration of the import process may vary based on the quantity of products and their associated images. Please be aware that this operation cannot be paused or interrupted.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          className: \"submit-button\",\n          onClick: handleBulkImport,\n          disabled: !(!importButtonDisabled && checkedNotes),\n          children: \"Import Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: hideUploadDialog,\n          style: {\n            marginLeft: '5px'\n          },\n          variant: \"contained\",\n          color: \"secondary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: isDeleteDialogOpen,\n      onClose: closeDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Delete Product\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Please enter the SKU of the product you want to delete:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"sku\",\n          label: \"SKU\",\n          type: \"text\",\n          fullWidth: true,\n          value: deleteSku,\n          onChange: handleDeleteSkuChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDeleteDialog,\n          color: \"primary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDelete,\n          color: \"primary\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(ProductList, \"16s4J5oo/BZZ5FY1RR/nxDJxFKE=\", false, function () {\n  return [useNavigate];\n});\n_c = ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getExportProducts", "read", "utils", "writeFile", "FontAwesomeIcon", "faFileImport", "faDownload", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "TextField", "TableContainer", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "Paper", "Grid", "TableSortLabel", "Tabs", "Tab", "InputAdornment", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "SaveAlt", "SaveAltIcon", "Search", "SearchIcon", "Refresh", "RefreshIcon", "CloudUpload", "CloudUploadIcon", "Add", "AddIcon", "Delete", "DeleteIcon", "useNavigate", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductList", "products", "_s", "fileInputRef", "navigate", "searchText", "setSearchText", "selectedProducts", "setSelectedProducts", "orderBy", "setOrderBy", "order", "setOrder", "tabValue", "setTabValue", "channelDetails", "setChannelDetails", "isChannelPublishedDialogOpen", "setIsChannelPublishedDialogOpen", "isChannelUnpublishedDialogOpen", "setIsChannelUnpublishedDialogOpen", "showImportDialog", "setShowImportDialog", "checkedNotes", "setCheckedNotes", "importButtonDisabled", "setImportButtonDisabled", "isDeleteDialogOpen", "setIsDeleteDialogOpen", "deleteSku", "setDeleteSku", "tabs", "handleRowClick", "product", "sku", "handleRefresh", "window", "location", "reload", "handleFileChange", "current", "files", "length", "hideUploadDialog", "pushProducts", "rows", "storedGroupCode", "localStorage", "getItem", "post", "process", "env", "REACT_APP_BASE_URL", "headers", "Authorization", "REACT_APP_ACCESS_TOKEN", "err", "console", "log", "message", "handleBulkImport", "file", "reader", "FileReader", "onload", "event", "wb", "target", "result", "sheets", "SheetNames", "sheet_to_json", "Sheets", "readAsA<PERSON>y<PERSON><PERSON>er", "exportSampleFileDownload", "book_new", "ws", "json_to_sheet", "headings", "data", "sheet_add_aoa", "sheet_add_json", "origin", "<PERSON><PERSON><PERSON><PERSON>", "book_append_sheet", "handleExport", "error", "handleImport", "handleAddProduct", "openDeleteDialog", "handleDeleteSkuChange", "value", "closeDeleteDialog", "handleChannelClick", "row", "channel_status", "res", "handleAddTab", "createSortHandler", "property", "isAsc", "filteredProducts", "filter", "searchFields", "some", "field", "toString", "toLowerCase", "includes", "sortedProducts", "sort", "a", "b", "comparison", "localeCompare", "getStatusColor", "status", "getChannelStatusColor", "renderProductsByStatus", "filteredProduct", "formatDate", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "getQuantityString", "displayQuantity", "inventory_quantity", "undefined", "quantity", "count", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "handleDelete", "container", "spacing", "item", "xs", "marginLeft", "variant", "startIcon", "onClick", "backgroundColor", "placeholder", "onChange", "e", "InputProps", "startAdornment", "position", "borderRadius", "sx", "width", "newValue", "scrollButtons", "map", "tab", "label", "icon", "component", "background", "active", "direction", "cursor", "type", "checked", "className", "transition", "onMouseEnter", "onMouseLeave", "title", "padding", "variantPrice", "price", "updated_at", "id", "open", "onClose", "onHide", "class", "ref", "accept", "for", "href", "disabled", "autoFocus", "margin", "fullWidth", "_c", "$RefreshReg$"], "sources": ["E:/digispin doc/repos/logistics/DigiConnector/digiconnectorfrontend/src/pages/Product/ProductList.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { getExportProducts } from '../../api/products';\r\nimport { read, utils, writeFile } from \"xlsx\";\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faFileImport, faDownload } from '@fortawesome/free-solid-svg-icons';\r\n\r\nimport {\r\n    Button,\r\n    Toolbar,\r\n    TextField,\r\n    TableContainer,\r\n    Table,\r\n    TableHead,\r\n    TableRow,\r\n    TableCell,\r\n    TableBody,\r\n    Paper,\r\n    Grid,\r\n    TableSortLabel,\r\n    Tabs,\r\n    Tab,\r\n    InputAdornment,\r\n    Menu,\r\n    MenuItem,\r\n    Dialog,\r\n    DialogTitle,\r\n    DialogContent,\r\n    DialogActions,\r\n    DialogContentText\r\n} from '@mui/material';\r\nimport {\r\n    SaveAlt as SaveAltIcon,\r\n    Search as SearchIcon,\r\n    Refresh as RefreshIcon,\r\n    CloudUpload as CloudUploadIcon,\r\n    Add as AddIcon,\r\n    Delete as DeleteIcon\r\n} from '@mui/icons-material';\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport axios from 'axios'\r\n\r\nexport const ProductList = ({ products }) => {\r\n    const fileInputRef = useRef(null);\r\n    const navigate = useNavigate();\r\n    const [searchText, setSearchText] = useState('');\r\n    const [selectedProducts, setSelectedProducts] = useState([]);\r\n    const [orderBy, setOrderBy] = useState(null);\r\n    const [order, setOrder] = useState('asc');\r\n    const [tabValue, setTabValue] = useState('All'); // Default to 'All' tab\r\n    const [channelDetails, setChannelDetails] = useState([]);\r\n    const [isChannelPublishedDialogOpen, setIsChannelPublishedDialogOpen] = useState(false);\r\n    const [isChannelUnpublishedDialogOpen, setIsChannelUnpublishedDialogOpen] = useState(false);\r\n    const [showImportDialog, setShowImportDialog] = useState(false);\r\n    const [checkedNotes, setCheckedNotes] = useState(false);\r\n    const [importButtonDisabled, setImportButtonDisabled] = useState(true);\r\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\r\n    const [deleteSku, setDeleteSku] = useState('');\r\n\r\n    const tabs = [\"All\", \"Active\", \"Archived\", \"Draft\", \"Published\", \"Unpublished\"];\r\n\r\n    const handleRowClick = (product) => {\r\n        navigate(`/products/edit/${product.sku}`);\r\n    };\r\n\r\n    const handleRefresh = () => {\r\n        window.location.reload();\r\n    };\r\n\r\n    const handleFileChange = () => {\r\n        if (fileInputRef.current && fileInputRef.current.files.length > 0) {\r\n            setImportButtonDisabled(false);\r\n        } else {\r\n            setImportButtonDisabled(true);\r\n        }\r\n    };\r\n\r\n    const hideUploadDialog = () => {\r\n        setShowImportDialog(false);\r\n    }\r\n\r\n    // Function to push products to the server\r\n    const pushProducts = async rows => {\r\n        try {\r\n            var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n            await axios.post(\r\n                `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product_varient_in_bulk?fby_user_id=${storedGroupCode}`,\r\n                rows,\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n                    }\r\n                }\r\n            );\r\n        } catch (err) {\r\n            console.log(err.message);\r\n        }\r\n    };\r\n\r\n    const handleBulkImport = () => {\r\n        if (!importButtonDisabled) {\r\n            const file = fileInputRef.current.files[0];\r\n            // if (file) {\r\n            //     onFileChange(file);\r\n            //     setShowImportDialog(false);\r\n            // }\r\n            // const file = event.target.files[0];\r\n            if (file) {\r\n                const reader = new FileReader();\r\n                reader.onload = async event => {\r\n                    const wb = read(event.target.result);\r\n                    const sheets = wb.SheetNames;\r\n                    if (sheets.length) {\r\n                        const rows = utils.sheet_to_json(wb.Sheets[sheets[0]]);\r\n                        await pushProducts(rows);\r\n                    }\r\n                };\r\n                reader.readAsArrayBuffer(file);\r\n            }\r\n        }\r\n    };\r\n\r\n    const exportSampleFileDownload = async () => {\r\n        const wb = utils.book_new();\r\n        const ws = utils.json_to_sheet([]);\r\n        let headings = [[\"id\", \"title\", \"description\", \"option_1_name\", \"option_1_value\", \"option_2_name\", \"option_2_value\",\r\n            \"sku\", \"gtin\", \"asin\", \"quantity\", \"price\", \"image_link\", \"brand\", \"tags\", \"category\", \"weight\", \"weight_unit\", \"height\", \"width\",\r\n            \"length\", \"dimensions_units\"\r\n        ]];\r\n        let data = [];\r\n        utils.sheet_add_aoa(ws, headings);\r\n        utils.sheet_add_json(ws, data, { origin: 'A2', skipHeader: true });\r\n        utils.book_append_sheet(wb, ws, 'Report');\r\n        writeFile(wb, `productSample.csv`);\r\n    };\r\n\r\n\r\n    const handleExport = async () => {\r\n        const wb = utils.book_new();\r\n        const ws = utils.json_to_sheet([]);\r\n        let headings = [[\"id\", \"title\", \"description\", \"option_1_name\", \"option_1_value\", \"option_2_name\", \"option_2_value\",\r\n            \"sku\", \"gtin\", \"asin\", \"quantity\", \"price\", \"image_link\", \"brand\", \"tags\", \"category\", \"weight\", \"weight_unit\", \"height\", \"width\",\r\n            \"length\", \"dimensions_units\"\r\n        ]];\r\n        let data = [];\r\n        try {\r\n            utils.sheet_add_aoa(ws, headings);\r\n            data = await getExportProducts();\r\n\r\n        } catch (error) {\r\n            data = [];\r\n            console.error('Error fetching products:', error);\r\n\r\n        }\r\n        utils.sheet_add_json(ws, data, { origin: 'A2', skipHeader: true });\r\n        utils.book_append_sheet(wb, ws, 'Report');\r\n        writeFile(wb, `product.csv`);\r\n    };\r\n\r\n    const handleImport = () => {\r\n        setShowImportDialog(true)\r\n    };\r\n\r\n    const handleAddProduct = () => {\r\n        navigate('/products/edit');\r\n    };\r\n\r\n    const openDeleteDialog = () => {\r\n        setIsDeleteDialogOpen(true);\r\n    };\r\n\r\n    const handleDeleteSkuChange = (event) => {\r\n        setDeleteSku(event.target.value);\r\n    };\r\n\r\n    // Function to close the delete confirmation dialog\r\n    const closeDeleteDialog = () => {\r\n        setIsDeleteDialogOpen(false);\r\n    };\r\n\r\n    const handleChannelClick = async (row) => {\r\n        if (row.channel_status == 'published') {\r\n            setIsChannelPublishedDialogOpen(true)\r\n        } else {\r\n            setIsChannelUnpublishedDialogOpen(true)\r\n        }\r\n        try {\r\n            var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n            const res = await axios.post(\r\n                `${process.env.REACT_APP_BASE_URL}/common/api/get_product?fby_user_id=${storedGroupCode}&sku=${row.sku}&isCreated=true`,\r\n                {},\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        Authorization: process.env.REACT_APP_ACCESS_TOKEN\r\n                    }\r\n                }\r\n            );\r\n            if (res.data.length > 0) {\r\n                setChannelDetails(res.data);\r\n                setIsChannelPublishedDialogOpen(true);\r\n            } else {\r\n                setChannelDetails([]);\r\n                setIsChannelUnpublishedDialogOpen(true);\r\n            }\r\n\r\n        } catch (error) {\r\n            console.error('Error fetching channel details:', error);\r\n            setChannelDetails([]);\r\n            setIsChannelUnpublishedDialogOpen(true);\r\n        }\r\n    };\r\n\r\n\r\n    const handleAddTab = () => {\r\n        console.log(\"Add new tab functionality goes here\");\r\n    };\r\n\r\n    const createSortHandler = (property) => () => {\r\n        const isAsc = orderBy === property && order === 'asc';\r\n        setOrder(isAsc ? 'desc' : 'asc');\r\n        setOrderBy(property);\r\n    };\r\n\r\n    const filteredProducts = products.filter(product => {\r\n        const searchFields = ['title', 'status', 'sku', 'inventory_quantity', 'price', 'channel', 'created_at'];\r\n        return searchFields.some(field =>\r\n            product[field] && product[field].toString().toLowerCase().includes(searchText.toLowerCase())\r\n        );\r\n    });\r\n\r\n    const sortedProducts = [...filteredProducts].sort((a, b) => {\r\n        if (orderBy) {\r\n            const comparison = a[orderBy].localeCompare(b[orderBy]);\r\n            return order === 'asc' ? comparison : -comparison;\r\n        }\r\n        return 0;\r\n    });\r\n\r\n    const getStatusColor = (status) => {\r\n        switch (status) {\r\n            case 'active':\r\n                return '#b9e5a4'; // Slightly darker green color for active status\r\n            case 'archived':\r\n                return '#e0e0e0'; // Slightly darker gray color for archived status\r\n            case 'draft':\r\n                return '#ffcc80'; // Slightly darker orange color for draft status\r\n            default:\r\n                return 'white'; // Default light color for other statuses\r\n        }\r\n    };\r\n\r\n    const getChannelStatusColor = (status) => {\r\n        switch (status) {\r\n            case 'published':\r\n                return '#b9e5a4';\r\n            default:\r\n                return '#e0e0e0';\r\n        }\r\n    }\r\n\r\n    const renderProductsByStatus = (status) => {\r\n        let filteredProduct = sortedProducts;\r\n        if (status !== 'All') {\r\n            if (status === 'Published') {\r\n                filteredProduct = filteredProduct.filter(product => product.channel_status === 'published');\r\n            } else if (status === 'Unpublished') {\r\n                filteredProduct = filteredProduct.filter(product => product.channel_status !== 'published');\r\n            } else {\r\n                filteredProduct = filteredProduct.filter(product => product.status === status.toLowerCase());\r\n            }\r\n        }\r\n        return filteredProduct;\r\n    };\r\n\r\n    const formatDate = (dateString) => {\r\n        if (!dateString) {\r\n            return \"\";\r\n        }\r\n        const date = new Date(dateString);\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, '0'); // Adding 1 because months are zero-indexed\r\n        const day = String(date.getDate()).padStart(2, '0');\r\n        return `${year}-${month}-${day}`;\r\n    };\r\n\r\n    const getQuantityString = (row) => {\r\n        // Use inventory_quantity if available, otherwise fall back to quantity\r\n        const displayQuantity = row.inventory_quantity !== undefined ? row.inventory_quantity : (row.quantity || 0);\r\n\r\n        if (row.count == 0) {\r\n            return displayQuantity;\r\n        } else {\r\n            return (\r\n                <span>\r\n                    {displayQuantity}<br />\r\n                    <span style={{ color: '#aaa' }}>{' ' + row.count || ''} variants</span>\r\n                </span>\r\n            );\r\n        }\r\n    }\r\n\r\n    const handleDelete = async () => {\r\n        closeDeleteDialog(); // Close the delete confirmation dialog\r\n\r\n        try {\r\n            var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n            await axios.post(\r\n                `${process.env.REACT_APP_BASE_URL}/common/api/delete_shopify_product?fby_user_id=${storedGroupCode}&sku=${deleteSku}`,\r\n                {},\r\n                {\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN} `\r\n                    }\r\n                }\r\n            );\r\n            // After successful deletion, you might want to refresh the product list\r\n            handleRefresh();\r\n        } catch (error) {\r\n            console.error('Error deleting product:', error);\r\n            // Handle error, display an error message, or take appropriate action\r\n        }\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Grid container spacing={2}>\r\n                <Grid item xs={12}>\r\n                    <Toolbar>\r\n                        <DialogTitle><b>Product List</b></DialogTitle>\r\n                        <div style={{ marginLeft: 'auto' }}>\r\n                            <Button\r\n                                variant=\"contained\"\r\n                                startIcon={<AddIcon />}\r\n                                onClick={handleAddProduct}\r\n                                style={{ backgroundColor: '#1976d2', color: '#fff', marginLeft: '8px' }}\r\n                            >\r\n                                Add Product\r\n                            </Button>\r\n                            <Button\r\n                                variant=\"contained\"\r\n                                startIcon={<RefreshIcon />}\r\n                                onClick={handleRefresh}\r\n                                style={{ backgroundColor: '#2196f3', marginLeft: '8px' }}\r\n                            >\r\n                                Refresh\r\n                            </Button>\r\n                            <Button\r\n                                variant=\"contained\"\r\n                                startIcon={<SaveAltIcon />}\r\n                                onClick={handleExport}\r\n                                style={{ backgroundColor: '#4caf50', marginLeft: '8px' }}\r\n                            >\r\n                                Export\r\n                            </Button>\r\n                            <Button\r\n                                variant=\"contained\"\r\n                                startIcon={<CloudUploadIcon />}\r\n                                onClick={handleImport}\r\n                                style={{ backgroundColor: '#f44336', marginLeft: '8px' }}\r\n                            >\r\n                                Import\r\n                            </Button>\r\n                            <Button\r\n                                variant=\"contained\"\r\n                                startIcon={<DeleteIcon />}\r\n                                onClick={openDeleteDialog}\r\n                                style={{ backgroundColor: '#f44336', marginLeft: '8px' }}\r\n                            >\r\n                                Delete\r\n                            </Button>\r\n                        </div>\r\n                    </Toolbar>\r\n                </Grid>\r\n                <Grid item xs={12}>\r\n                    <div style={{ marginLeft: 'auto' }}>\r\n                        <TextField\r\n                            variant=\"outlined\"\r\n                            placeholder=\"Search...\"\r\n                            onChange={(e) => setSearchText(e.target.value)}\r\n                            InputProps={{\r\n                                startAdornment: (\r\n                                    <InputAdornment position=\"start\">\r\n                                        <SearchIcon />\r\n                                    </InputAdornment>\r\n                                ),\r\n                                style: { borderRadius: '2px' }\r\n                            }}\r\n                            sx={{ width: 300 }}  // Adjust the width as needed\r\n                        />\r\n                        <Tabs\r\n                            value={tabValue}\r\n                            onChange={(e, newValue) => setTabValue(newValue)}\r\n                            aria-label=\"product-tabs\"\r\n                            variant=\"scrollable\"\r\n                            scrollButtons=\"auto\"\r\n                        >\r\n                            {tabs.map(tab => (\r\n                                <Tab key={tab} label={tab} value={tab} />\r\n                            ))}\r\n                            <Tab\r\n                                icon={<AddIcon />}\r\n                                aria-label=\"add-tab\"\r\n                                onClick={handleAddTab}\r\n                            />\r\n                        </Tabs>\r\n                    </div>\r\n                </Grid>\r\n                <Grid item xs={12}>\r\n                    <TableContainer component={Paper} sx={{ width: '100%' }}>\r\n                        <Table>\r\n                            <TableHead sx={{ background: '#f5f5f5' }}>\r\n                                <TableRow>\r\n                                    <TableCell></TableCell>\r\n                                    <TableCell>\r\n                                        <TableSortLabel\r\n                                            active={orderBy === 'title'}\r\n                                            direction={orderBy === 'title' ? order : 'asc'}\r\n                                            onClick={createSortHandler('title')}\r\n                                        >\r\n                                            Product\r\n                                        </TableSortLabel>\r\n                                    </TableCell>\r\n                                    <TableCell>Product Status</TableCell>\r\n                                    <TableCell>\r\n                                        <TableSortLabel\r\n                                            active={orderBy === 'sku'}\r\n                                            direction={orderBy === 'sku' ? order : 'asc'}\r\n                                            onClick={createSortHandler('sku')}\r\n                                        >\r\n                                            SKU\r\n                                        </TableSortLabel>\r\n                                    </TableCell>\r\n                                    <TableCell>Quantity</TableCell>\r\n                                    <TableCell>Price</TableCell>\r\n                                    <TableCell>Channel Status</TableCell>\r\n                                    <TableCell>Updated</TableCell>\r\n                                </TableRow>\r\n                            </TableHead>\r\n                            <TableBody>\r\n                                {renderProductsByStatus(tabValue).map((product) => (\r\n                                    <TableRow key={product.id} style={{ cursor: 'pointer' }}>\r\n                                        <TableCell>\r\n                                            <input\r\n                                                type=\"checkbox\"\r\n                                                checked={selectedProducts.includes(product)}\r\n                                                onChange={() => {\r\n                                                    if (selectedProducts.includes(product)) {\r\n                                                        setSelectedProducts(selectedProducts.filter(item => item !== product));\r\n                                                    } else {\r\n                                                        setSelectedProducts([...selectedProducts, product]);\r\n                                                    }\r\n                                                }}\r\n                                            />\r\n                                        </TableCell>\r\n                                        <TableCell>\r\n                                            <span\r\n                                                className=\"product-title\"\r\n                                                onClick={() => handleRowClick(product)}\r\n                                                style={{\r\n                                                    cursor: 'pointer',\r\n                                                    color: '#1976d2', // Blue color\r\n                                                    transition: 'color 0.3s'\r\n                                                }}\r\n                                                onMouseEnter={(e) => e.target.style.color = '#115293'} // Darker blue when hovered\r\n                                                onMouseLeave={(e) => e.target.style.color = '#1976d2'} // Return to original color when not hovered\r\n                                            >\r\n                                                {product.title}\r\n                                            </span>\r\n                                        </TableCell>\r\n                                        <TableCell>\r\n                                            <span style={{ backgroundColor: getStatusColor(product.status), borderRadius: '4px', padding: '4px' }}>\r\n                                                {product.status}\r\n                                            </span>\r\n                                        </TableCell>\r\n                                        <TableCell>{product.sku}</TableCell>\r\n                                        <TableCell>{getQuantityString(product)}</TableCell>\r\n                                        <TableCell>{product.variantPrice ? product.variantPrice : product.price}</TableCell>\r\n                                        <TableCell>\r\n                                            <span\r\n                                                style={{\r\n                                                    backgroundColor: getChannelStatusColor(product.channel_status),\r\n                                                    borderRadius: '4px',\r\n                                                    padding: '4px',\r\n                                                    cursor: 'pointer'\r\n                                                }}\r\n                                                onClick={handleChannelClick}\r\n                                            >\r\n                                                {product.channel_status}\r\n                                            </span>\r\n                                        </TableCell>\r\n                                        <TableCell>{formatDate(product.updated_at)}</TableCell>\r\n                                    </TableRow>\r\n                                ))}\r\n                            </TableBody>\r\n                        </Table>\r\n                    </TableContainer>\r\n                </Grid>\r\n                <Dialog open={isChannelPublishedDialogOpen} onClose={() => setIsChannelPublishedDialogOpen(false)}>\r\n                    <DialogTitle>Published Channel Details</DialogTitle>\r\n                    <DialogContent>\r\n                        {'Display Published channel details here'}\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                        <Button onClick={() => setIsChannelPublishedDialogOpen(false)}>Close</Button>\r\n                    </DialogActions>\r\n                </Dialog>\r\n\r\n                <Dialog open={isChannelUnpublishedDialogOpen} onClose={() => setIsChannelUnpublishedDialogOpen(false)}>\r\n                    <DialogTitle>Unpublished Channel Details</DialogTitle>\r\n                    <DialogContent>\r\n                        {'Display Unpublished channel details here'}\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                        <Button onClick={() => setIsChannelUnpublishedDialogOpen(false)}>Close</Button>\r\n                    </DialogActions>\r\n                </Dialog>\r\n            </Grid>\r\n            <Dialog open={showImportDialog} onHide={hideUploadDialog}>\r\n                <div className=\"upload-dialog-content\">\r\n                    <h2>Import Products</h2>\r\n                    <div className=\"upload-options\">\r\n                        <div className=\"local-upload\">\r\n                            <div class=\"drag-drop-box\">\r\n                                <input id=\"file-upload\" type=\"file\" class=\"file-upload-input\" ref={fileInputRef} onChange={handleFileChange} accept=\".csv\" />\r\n                                <label for=\"file-upload\" class=\"file-upload-label\">\r\n                                    <span class=\"plus-icon\">+</span>\r\n                                    <span class=\"drag-drop-text\">\r\n                                        <FontAwesomeIcon icon={faFileImport} className=\"upload-icon\" />\r\n                                        Upload a CSV file or Drag & Drop it\r\n                                    </span>\r\n                                </label>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"template-download\" onClick={exportSampleFileDownload}>\r\n                            <a href=\"#\" className=\"download-template\">\r\n                                <FontAwesomeIcon icon={faDownload} className=\"download-icon\" />\r\n                                Download Template\r\n                            </a>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"import-notes\">\r\n                        <input type=\"checkbox\" id=\"show-notes\" checked={checkedNotes} onChange={() => setCheckedNotes(!checkedNotes)} />\r\n                        <p>\r\n                            Prior to importing your products, please ensure that you have assigned a unique SKU code to each product or variant.\r\n                            Digihub will link products with matching SKU codes. The duration of the import process may vary based on the quantity of products\r\n                            and their associated images. Please be aware that this operation cannot be paused or interrupted.\r\n                        </p>\r\n\r\n                    </div>\r\n                    <Button className=\"submit-button\" onClick={handleBulkImport} disabled={!(!importButtonDisabled && checkedNotes)}>\r\n                        Import Products\r\n                    </Button>\r\n                    <Button onClick={hideUploadDialog} style={{ marginLeft: '5px' }} variant=\"contained\" color=\"secondary\">\r\n                        Cancel\r\n                    </Button>\r\n                </div>\r\n            </Dialog>\r\n            <Dialog open={isDeleteDialogOpen} onClose={closeDeleteDialog}>\r\n                <DialogTitle>Delete Product</DialogTitle>\r\n                <DialogContent>\r\n                    <DialogContentText>\r\n                        Please enter the SKU of the product you want to delete:\r\n                    </DialogContentText>\r\n                    <TextField\r\n                        autoFocus\r\n                        margin=\"dense\"\r\n                        id=\"sku\"\r\n                        label=\"SKU\"\r\n                        type=\"text\"\r\n                        fullWidth\r\n                        value={deleteSku}\r\n                        onChange={handleDeleteSkuChange}\r\n                    />\r\n                </DialogContent>\r\n                <DialogActions>\r\n                    <Button onClick={closeDeleteDialog} color=\"primary\">\r\n                        Cancel\r\n                    </Button>\r\n                    <Button onClick={handleDelete} color=\"primary\">\r\n                        Delete\r\n                    </Button>\r\n                </DialogActions>\r\n            </Dialog>\r\n        </>\r\n\r\n    );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,IAAI,EAAEC,KAAK,EAAEC,SAAS,QAAQ,MAAM;AAC7C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,YAAY,EAAEC,UAAU,QAAQ,mCAAmC;AAE5E,SACIC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,cAAc,EACdC,KAAK,EACLC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACd,eAAe;AACtB,SACIC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,OAAO,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAMC,YAAY,GAAGnD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMoD,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6D,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACjD,MAAM,CAACiE,cAAc,EAAEC,iBAAiB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmE,4BAA4B,EAAEC,+BAA+B,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACvF,MAAM,CAACqE,8BAA8B,EAAEC,iCAAiC,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC3F,MAAM,CAACuE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC6E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAMiF,IAAI,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,CAAC;EAE/E,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAChC7B,QAAQ,CAAC,kBAAkB6B,OAAO,CAACC,GAAG,EAAE,CAAC;EAC7C,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAIpC,YAAY,CAACqC,OAAO,IAAIrC,YAAY,CAACqC,OAAO,CAACC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/DhB,uBAAuB,CAAC,KAAK,CAAC;IAClC,CAAC,MAAM;MACHA,uBAAuB,CAAC,IAAI,CAAC;IACjC;EACJ,CAAC;EAED,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;IAC3BrB,mBAAmB,CAAC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMsB,YAAY,GAAG,MAAMC,IAAI,IAAI;IAC/B,IAAI;MACA,IAAIC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACvD,MAAMrD,KAAK,CAACsD,IAAI,CACZ,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,gEAAgEN,eAAe,EAAE,EAClHD,IAAI,EACJ;QACIQ,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUJ,OAAO,CAACC,GAAG,CAACI,sBAAsB;QAC/D;MACJ,CACJ,CAAC;IACL,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACC,GAAG,CAACF,GAAG,CAACG,OAAO,CAAC;IAC5B;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACnC,oBAAoB,EAAE;MACvB,MAAMoC,IAAI,GAAG1D,YAAY,CAACqC,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC;MAC1C;MACA;MACA;MACA;MACA;MACA,IAAIoB,IAAI,EAAE;QACN,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAG,MAAMC,KAAK,IAAI;UAC3B,MAAMC,EAAE,GAAGhH,IAAI,CAAC+G,KAAK,CAACE,MAAM,CAACC,MAAM,CAAC;UACpC,MAAMC,MAAM,GAAGH,EAAE,CAACI,UAAU;UAC5B,IAAID,MAAM,CAAC3B,MAAM,EAAE;YACf,MAAMG,IAAI,GAAG1F,KAAK,CAACoH,aAAa,CAACL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,MAAMzB,YAAY,CAACC,IAAI,CAAC;UAC5B;QACJ,CAAC;QACDiB,MAAM,CAACW,iBAAiB,CAACZ,IAAI,CAAC;MAClC;IACJ;EACJ,CAAC;EAED,MAAMa,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IACzC,MAAMR,EAAE,GAAG/G,KAAK,CAACwH,QAAQ,CAAC,CAAC;IAC3B,MAAMC,EAAE,GAAGzH,KAAK,CAAC0H,aAAa,CAAC,EAAE,CAAC;IAClC,IAAIC,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAC/G,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EACjI,QAAQ,EAAE,kBAAkB,CAC/B,CAAC;IACF,IAAIC,IAAI,GAAG,EAAE;IACb5H,KAAK,CAAC6H,aAAa,CAACJ,EAAE,EAAEE,QAAQ,CAAC;IACjC3H,KAAK,CAAC8H,cAAc,CAACL,EAAE,EAAEG,IAAI,EAAE;MAAEG,MAAM,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,CAAC;IAClEhI,KAAK,CAACiI,iBAAiB,CAAClB,EAAE,EAAEU,EAAE,EAAE,QAAQ,CAAC;IACzCxH,SAAS,CAAC8G,EAAE,EAAE,mBAAmB,CAAC;EACtC,CAAC;EAGD,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMnB,EAAE,GAAG/G,KAAK,CAACwH,QAAQ,CAAC,CAAC;IAC3B,MAAMC,EAAE,GAAGzH,KAAK,CAAC0H,aAAa,CAAC,EAAE,CAAC;IAClC,IAAIC,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAC/G,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EACjI,QAAQ,EAAE,kBAAkB,CAC/B,CAAC;IACF,IAAIC,IAAI,GAAG,EAAE;IACb,IAAI;MACA5H,KAAK,CAAC6H,aAAa,CAACJ,EAAE,EAAEE,QAAQ,CAAC;MACjCC,IAAI,GAAG,MAAM9H,iBAAiB,CAAC,CAAC;IAEpC,CAAC,CAAC,OAAOqI,KAAK,EAAE;MACZP,IAAI,GAAG,EAAE;MACTtB,OAAO,CAAC6B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAEpD;IACAnI,KAAK,CAAC8H,cAAc,CAACL,EAAE,EAAEG,IAAI,EAAE;MAAEG,MAAM,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC,CAAC;IAClEhI,KAAK,CAACiI,iBAAiB,CAAClB,EAAE,EAAEU,EAAE,EAAE,QAAQ,CAAC;IACzCxH,SAAS,CAAC8G,EAAE,EAAE,aAAa,CAAC;EAChC,CAAC;EAED,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACvBjE,mBAAmB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMkE,gBAAgB,GAAGA,CAAA,KAAM;IAC3BpF,QAAQ,CAAC,gBAAgB,CAAC;EAC9B,CAAC;EAED,MAAMqF,gBAAgB,GAAGA,CAAA,KAAM;IAC3B7D,qBAAqB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAM8D,qBAAqB,GAAIzB,KAAK,IAAK;IACrCnC,YAAY,CAACmC,KAAK,CAACE,MAAM,CAACwB,KAAK,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC5BhE,qBAAqB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMiE,kBAAkB,GAAG,MAAOC,GAAG,IAAK;IACtC,IAAIA,GAAG,CAACC,cAAc,IAAI,WAAW,EAAE;MACnC7E,+BAA+B,CAAC,IAAI,CAAC;IACzC,CAAC,MAAM;MACHE,iCAAiC,CAAC,IAAI,CAAC;IAC3C;IACA,IAAI;MACA,IAAI0B,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACvD,MAAMgD,GAAG,GAAG,MAAMrG,KAAK,CAACsD,IAAI,CACxB,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,uCAAuCN,eAAe,QAAQgD,GAAG,CAAC5D,GAAG,iBAAiB,EACvH,CAAC,CAAC,EACF;QACImB,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAEJ,OAAO,CAACC,GAAG,CAACI;QAC/B;MACJ,CACJ,CAAC;MACD,IAAIyC,GAAG,CAACjB,IAAI,CAACrC,MAAM,GAAG,CAAC,EAAE;QACrB1B,iBAAiB,CAACgF,GAAG,CAACjB,IAAI,CAAC;QAC3B7D,+BAA+B,CAAC,IAAI,CAAC;MACzC,CAAC,MAAM;QACHF,iBAAiB,CAAC,EAAE,CAAC;QACrBI,iCAAiC,CAAC,IAAI,CAAC;MAC3C;IAEJ,CAAC,CAAC,OAAOkE,KAAK,EAAE;MACZ7B,OAAO,CAAC6B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDtE,iBAAiB,CAAC,EAAE,CAAC;MACrBI,iCAAiC,CAAC,IAAI,CAAC;IAC3C;EACJ,CAAC;EAGD,MAAM6E,YAAY,GAAGA,CAAA,KAAM;IACvBxC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACtD,CAAC;EAED,MAAMwC,iBAAiB,GAAIC,QAAQ,IAAK,MAAM;IAC1C,MAAMC,KAAK,GAAG3F,OAAO,KAAK0F,QAAQ,IAAIxF,KAAK,KAAK,KAAK;IACrDC,QAAQ,CAACwF,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAChC1F,UAAU,CAACyF,QAAQ,CAAC;EACxB,CAAC;EAED,MAAME,gBAAgB,GAAGpG,QAAQ,CAACqG,MAAM,CAACrE,OAAO,IAAI;IAChD,MAAMsE,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,oBAAoB,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC;IACvG,OAAOA,YAAY,CAACC,IAAI,CAACC,KAAK,IAC1BxE,OAAO,CAACwE,KAAK,CAAC,IAAIxE,OAAO,CAACwE,KAAK,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvG,UAAU,CAACsG,WAAW,CAAC,CAAC,CAC/F,CAAC;EACL,CAAC,CAAC;EAEF,MAAME,cAAc,GAAG,CAAC,GAAGR,gBAAgB,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACxD,IAAIvG,OAAO,EAAE;MACT,MAAMwG,UAAU,GAAGF,CAAC,CAACtG,OAAO,CAAC,CAACyG,aAAa,CAACF,CAAC,CAACvG,OAAO,CAAC,CAAC;MACvD,OAAOE,KAAK,KAAK,KAAK,GAAGsG,UAAU,GAAG,CAACA,UAAU;IACrD;IACA,OAAO,CAAC;EACZ,CAAC,CAAC;EAEF,MAAME,cAAc,GAAIC,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACV,KAAK,QAAQ;QACT,OAAO,SAAS;MAAE;MACtB,KAAK,UAAU;QACX,OAAO,SAAS;MAAE;MACtB,KAAK,OAAO;QACR,OAAO,SAAS;MAAE;MACtB;QACI,OAAO,OAAO;MAAE;IACxB;EACJ,CAAC;EAED,MAAMC,qBAAqB,GAAID,MAAM,IAAK;IACtC,QAAQA,MAAM;MACV,KAAK,WAAW;QACZ,OAAO,SAAS;MACpB;QACI,OAAO,SAAS;IACxB;EACJ,CAAC;EAED,MAAME,sBAAsB,GAAIF,MAAM,IAAK;IACvC,IAAIG,eAAe,GAAGV,cAAc;IACpC,IAAIO,MAAM,KAAK,KAAK,EAAE;MAClB,IAAIA,MAAM,KAAK,WAAW,EAAE;QACxBG,eAAe,GAAGA,eAAe,CAACjB,MAAM,CAACrE,OAAO,IAAIA,OAAO,CAAC8D,cAAc,KAAK,WAAW,CAAC;MAC/F,CAAC,MAAM,IAAIqB,MAAM,KAAK,aAAa,EAAE;QACjCG,eAAe,GAAGA,eAAe,CAACjB,MAAM,CAACrE,OAAO,IAAIA,OAAO,CAAC8D,cAAc,KAAK,WAAW,CAAC;MAC/F,CAAC,MAAM;QACHwB,eAAe,GAAGA,eAAe,CAACjB,MAAM,CAACrE,OAAO,IAAIA,OAAO,CAACmF,MAAM,KAAKA,MAAM,CAACT,WAAW,CAAC,CAAC,CAAC;MAChG;IACJ;IACA,OAAOY,eAAe;EAC1B,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IAC/B,IAAI,CAACA,UAAU,EAAE;MACb,OAAO,EAAE;IACb;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EACpC,CAAC;EAED,MAAME,iBAAiB,GAAItC,GAAG,IAAK;IAC/B;IACA,MAAMuC,eAAe,GAAGvC,GAAG,CAACwC,kBAAkB,KAAKC,SAAS,GAAGzC,GAAG,CAACwC,kBAAkB,GAAIxC,GAAG,CAAC0C,QAAQ,IAAI,CAAE;IAE3G,IAAI1C,GAAG,CAAC2C,KAAK,IAAI,CAAC,EAAE;MAChB,OAAOJ,eAAe;IAC1B,CAAC,MAAM;MACH,oBACIxI,OAAA;QAAA6I,QAAA,GACKL,eAAe,eAACxI,OAAA;UAAA8I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvBjJ,OAAA;UAAMkJ,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAN,QAAA,GAAE,GAAG,GAAG5C,GAAG,CAAC2C,KAAK,IAAI,EAAE,EAAC,WAAS;QAAA;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAEf;EACJ,CAAC;EAED,MAAMG,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7BrD,iBAAiB,CAAC,CAAC,CAAC,CAAC;;IAErB,IAAI;MACA,IAAI9C,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACvD,MAAMrD,KAAK,CAACsD,IAAI,CACZ,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,kDAAkDN,eAAe,QAAQjB,SAAS,EAAE,EACrH,CAAC,CAAC,EACF;QACIwB,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUJ,OAAO,CAACC,GAAG,CAACI,sBAAsB;QAC/D;MACJ,CACJ,CAAC;MACD;MACApB,aAAa,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACZ7B,OAAO,CAAC6B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;IACJ;EACJ,CAAC;EAED,oBACIzF,OAAA,CAAAE,SAAA;IAAA2I,QAAA,gBACI7I,OAAA,CAAC3B,IAAI;MAACgL,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAT,QAAA,gBACvB7I,OAAA,CAAC3B,IAAI;QAACkL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAX,QAAA,eACd7I,OAAA,CAACpC,OAAO;UAAAiL,QAAA,gBACJ7I,OAAA,CAACnB,WAAW;YAAAgK,QAAA,eAAC7I,OAAA;cAAA6I,QAAA,EAAG;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9CjJ,OAAA;YAAKkJ,KAAK,EAAE;cAAEO,UAAU,EAAE;YAAO,CAAE;YAAAZ,QAAA,gBAC/B7I,OAAA,CAACrC,MAAM;cACH+L,OAAO,EAAC,WAAW;cACnBC,SAAS,eAAE3J,OAAA,CAACN,OAAO;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBW,OAAO,EAAEjE,gBAAiB;cAC1BuD,KAAK,EAAE;gBAAEW,eAAe,EAAE,SAAS;gBAAEV,KAAK,EAAE,MAAM;gBAAEM,UAAU,EAAE;cAAM,CAAE;cAAAZ,QAAA,EAC3E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrC,MAAM;cACH+L,OAAO,EAAC,WAAW;cACnBC,SAAS,eAAE3J,OAAA,CAACV,WAAW;gBAAAwJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BW,OAAO,EAAEtH,aAAc;cACvB4G,KAAK,EAAE;gBAAEW,eAAe,EAAE,SAAS;gBAAEJ,UAAU,EAAE;cAAM,CAAE;cAAAZ,QAAA,EAC5D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrC,MAAM;cACH+L,OAAO,EAAC,WAAW;cACnBC,SAAS,eAAE3J,OAAA,CAACd,WAAW;gBAAA4J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BW,OAAO,EAAEpE,YAAa;cACtB0D,KAAK,EAAE;gBAAEW,eAAe,EAAE,SAAS;gBAAEJ,UAAU,EAAE;cAAM,CAAE;cAAAZ,QAAA,EAC5D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrC,MAAM;cACH+L,OAAO,EAAC,WAAW;cACnBC,SAAS,eAAE3J,OAAA,CAACR,eAAe;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BW,OAAO,EAAElE,YAAa;cACtBwD,KAAK,EAAE;gBAAEW,eAAe,EAAE,SAAS;gBAAEJ,UAAU,EAAE;cAAM,CAAE;cAAAZ,QAAA,EAC5D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrC,MAAM;cACH+L,OAAO,EAAC,WAAW;cACnBC,SAAS,eAAE3J,OAAA,CAACJ,UAAU;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BW,OAAO,EAAEhE,gBAAiB;cAC1BsD,KAAK,EAAE;gBAAEW,eAAe,EAAE,SAAS;gBAAEJ,UAAU,EAAE;cAAM,CAAE;cAAAZ,QAAA,EAC5D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACPjJ,OAAA,CAAC3B,IAAI;QAACkL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAX,QAAA,eACd7I,OAAA;UAAKkJ,KAAK,EAAE;YAAEO,UAAU,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBAC/B7I,OAAA,CAACnC,SAAS;YACN6L,OAAO,EAAC,UAAU;YAClBI,WAAW,EAAC,WAAW;YACvBC,QAAQ,EAAGC,CAAC,IAAKvJ,aAAa,CAACuJ,CAAC,CAAC1F,MAAM,CAACwB,KAAK,CAAE;YAC/CmE,UAAU,EAAE;cACRC,cAAc,eACVlK,OAAA,CAACvB,cAAc;gBAAC0L,QAAQ,EAAC,OAAO;gBAAAtB,QAAA,eAC5B7I,OAAA,CAACZ,UAAU;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACnB;cACDC,KAAK,EAAE;gBAAEkB,YAAY,EAAE;cAAM;YACjC,CAAE;YACFC,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE,CAAE;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFjJ,OAAA,CAACzB,IAAI;YACDuH,KAAK,EAAE9E,QAAS;YAChB+I,QAAQ,EAAEA,CAACC,CAAC,EAAEO,QAAQ,KAAKtJ,WAAW,CAACsJ,QAAQ,CAAE;YACjD,cAAW,cAAc;YACzBb,OAAO,EAAC,YAAY;YACpBc,aAAa,EAAC,MAAM;YAAA3B,QAAA,GAEnB3G,IAAI,CAACuI,GAAG,CAACC,GAAG,iBACT1K,OAAA,CAACxB,GAAG;cAAWmM,KAAK,EAAED,GAAI;cAAC5E,KAAK,EAAE4E;YAAI,GAA5BA,GAAG;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA2B,CAC3C,CAAC,eACFjJ,OAAA,CAACxB,GAAG;cACAoM,IAAI,eAAE5K,OAAA,CAACN,OAAO;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAClB,cAAW,SAAS;cACpBW,OAAO,EAAExD;YAAa;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjJ,OAAA,CAAC3B,IAAI;QAACkL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAX,QAAA,eACd7I,OAAA,CAAClC,cAAc;UAAC+M,SAAS,EAAEzM,KAAM;UAACiM,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAzB,QAAA,eACpD7I,OAAA,CAACjC,KAAK;YAAA8K,QAAA,gBACF7I,OAAA,CAAChC,SAAS;cAACqM,EAAE,EAAE;gBAAES,UAAU,EAAE;cAAU,CAAE;cAAAjC,QAAA,eACrC7I,OAAA,CAAC/B,QAAQ;gBAAA4K,QAAA,gBACL7I,OAAA,CAAC9B,SAAS;kBAAA4K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvBjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,eACN7I,OAAA,CAAC1B,cAAc;oBACXyM,MAAM,EAAEnK,OAAO,KAAK,OAAQ;oBAC5BoK,SAAS,EAAEpK,OAAO,KAAK,OAAO,GAAGE,KAAK,GAAG,KAAM;oBAC/C8I,OAAO,EAAEvD,iBAAiB,CAAC,OAAO,CAAE;oBAAAwC,QAAA,EACvC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAgB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACZjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrCjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,eACN7I,OAAA,CAAC1B,cAAc;oBACXyM,MAAM,EAAEnK,OAAO,KAAK,KAAM;oBAC1BoK,SAAS,EAAEpK,OAAO,KAAK,KAAK,GAAGE,KAAK,GAAG,KAAM;oBAC7C8I,OAAO,EAAEvD,iBAAiB,CAAC,KAAK,CAAE;oBAAAwC,QAAA,EACrC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAgB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACZjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/BjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5BjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrCjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZjJ,OAAA,CAAC7B,SAAS;cAAA0K,QAAA,EACLpB,sBAAsB,CAACzG,QAAQ,CAAC,CAACyJ,GAAG,CAAErI,OAAO,iBAC1CpC,OAAA,CAAC/B,QAAQ;gBAAkBiL,KAAK,EAAE;kBAAE+B,MAAM,EAAE;gBAAU,CAAE;gBAAApC,QAAA,gBACpD7I,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,eACN7I,OAAA;oBACIkL,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAEzK,gBAAgB,CAACqG,QAAQ,CAAC3E,OAAO,CAAE;oBAC5C2H,QAAQ,EAAEA,CAAA,KAAM;sBACZ,IAAIrJ,gBAAgB,CAACqG,QAAQ,CAAC3E,OAAO,CAAC,EAAE;wBACpCzB,mBAAmB,CAACD,gBAAgB,CAAC+F,MAAM,CAAC8C,IAAI,IAAIA,IAAI,KAAKnH,OAAO,CAAC,CAAC;sBAC1E,CAAC,MAAM;wBACHzB,mBAAmB,CAAC,CAAC,GAAGD,gBAAgB,EAAE0B,OAAO,CAAC,CAAC;sBACvD;oBACJ;kBAAE;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,eACN7I,OAAA;oBACIoL,SAAS,EAAC,eAAe;oBACzBxB,OAAO,EAAEA,CAAA,KAAMzH,cAAc,CAACC,OAAO,CAAE;oBACvC8G,KAAK,EAAE;sBACH+B,MAAM,EAAE,SAAS;sBACjB9B,KAAK,EAAE,SAAS;sBAAE;sBAClBkC,UAAU,EAAE;oBAChB,CAAE;oBACFC,YAAY,EAAGtB,CAAC,IAAKA,CAAC,CAAC1F,MAAM,CAAC4E,KAAK,CAACC,KAAK,GAAG,SAAU,CAAC;oBAAA;oBACvDoC,YAAY,EAAGvB,CAAC,IAAKA,CAAC,CAAC1F,MAAM,CAAC4E,KAAK,CAACC,KAAK,GAAG,SAAU,CAAC;oBAAA;oBAAAN,QAAA,EAEtDzG,OAAO,CAACoJ;kBAAK;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACZjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,eACN7I,OAAA;oBAAMkJ,KAAK,EAAE;sBAAEW,eAAe,EAAEvC,cAAc,CAAClF,OAAO,CAACmF,MAAM,CAAC;sBAAE6C,YAAY,EAAE,KAAK;sBAAEqB,OAAO,EAAE;oBAAM,CAAE;oBAAA5C,QAAA,EACjGzG,OAAO,CAACmF;kBAAM;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACZjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAEzG,OAAO,CAACC;gBAAG;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAEN,iBAAiB,CAACnG,OAAO;gBAAC;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnDjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAEzG,OAAO,CAACsJ,YAAY,GAAGtJ,OAAO,CAACsJ,YAAY,GAAGtJ,OAAO,CAACuJ;gBAAK;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpFjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,eACN7I,OAAA;oBACIkJ,KAAK,EAAE;sBACHW,eAAe,EAAErC,qBAAqB,CAACpF,OAAO,CAAC8D,cAAc,CAAC;sBAC9DkE,YAAY,EAAE,KAAK;sBACnBqB,OAAO,EAAE,KAAK;sBACdR,MAAM,EAAE;oBACZ,CAAE;oBACFrB,OAAO,EAAE5D,kBAAmB;oBAAA6C,QAAA,EAE3BzG,OAAO,CAAC8D;kBAAc;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACZjJ,OAAA,CAAC9B,SAAS;kBAAA2K,QAAA,EAAElB,UAAU,CAACvF,OAAO,CAACwJ,UAAU;gBAAC;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,GAlD5C7G,OAAO,CAACyJ,EAAE;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmDf,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACPjJ,OAAA,CAACpB,MAAM;QAACkN,IAAI,EAAE1K,4BAA6B;QAAC2K,OAAO,EAAEA,CAAA,KAAM1K,+BAA+B,CAAC,KAAK,CAAE;QAAAwH,QAAA,gBAC9F7I,OAAA,CAACnB,WAAW;UAAAgK,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACpDjJ,OAAA,CAAClB,aAAa;UAAA+J,QAAA,EACT;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAChBjJ,OAAA,CAACjB,aAAa;UAAA8J,QAAA,eACV7I,OAAA,CAACrC,MAAM;YAACiM,OAAO,EAAEA,CAAA,KAAMvI,+BAA+B,CAAC,KAAK,CAAE;YAAAwH,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAETjJ,OAAA,CAACpB,MAAM;QAACkN,IAAI,EAAExK,8BAA+B;QAACyK,OAAO,EAAEA,CAAA,KAAMxK,iCAAiC,CAAC,KAAK,CAAE;QAAAsH,QAAA,gBAClG7I,OAAA,CAACnB,WAAW;UAAAgK,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtDjJ,OAAA,CAAClB,aAAa;UAAA+J,QAAA,EACT;QAA0C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAChBjJ,OAAA,CAACjB,aAAa;UAAA8J,QAAA,eACV7I,OAAA,CAACrC,MAAM;YAACiM,OAAO,EAAEA,CAAA,KAAMrI,iCAAiC,CAAC,KAAK,CAAE;YAAAsH,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eACPjJ,OAAA,CAACpB,MAAM;MAACkN,IAAI,EAAEtK,gBAAiB;MAACwK,MAAM,EAAElJ,gBAAiB;MAAA+F,QAAA,eACrD7I,OAAA;QAAKoL,SAAS,EAAC,uBAAuB;QAAAvC,QAAA,gBAClC7I,OAAA;UAAA6I,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBjJ,OAAA;UAAKoL,SAAS,EAAC,gBAAgB;UAAAvC,QAAA,gBAC3B7I,OAAA;YAAKoL,SAAS,EAAC,cAAc;YAAAvC,QAAA,eACzB7I,OAAA;cAAKiM,KAAK,EAAC,eAAe;cAAApD,QAAA,gBACtB7I,OAAA;gBAAO6L,EAAE,EAAC,aAAa;gBAACX,IAAI,EAAC,MAAM;gBAACe,KAAK,EAAC,mBAAmB;gBAACC,GAAG,EAAE5L,YAAa;gBAACyJ,QAAQ,EAAErH,gBAAiB;gBAACyJ,MAAM,EAAC;cAAM;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7HjJ,OAAA;gBAAOoM,GAAG,EAAC,aAAa;gBAACH,KAAK,EAAC,mBAAmB;gBAAApD,QAAA,gBAC9C7I,OAAA;kBAAMiM,KAAK,EAAC,WAAW;kBAAApD,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChCjJ,OAAA;kBAAMiM,KAAK,EAAC,gBAAgB;kBAAApD,QAAA,gBACxB7I,OAAA,CAACxC,eAAe;oBAACoN,IAAI,EAAEnN,YAAa;oBAAC2N,SAAS,EAAC;kBAAa;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uCAEnE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNjJ,OAAA;YAAKoL,SAAS,EAAC,mBAAmB;YAACxB,OAAO,EAAE/E,wBAAyB;YAAAgE,QAAA,eACjE7I,OAAA;cAAGqM,IAAI,EAAC,GAAG;cAACjB,SAAS,EAAC,mBAAmB;cAAAvC,QAAA,gBACrC7I,OAAA,CAACxC,eAAe;gBAACoN,IAAI,EAAElN,UAAW;gBAAC0N,SAAS,EAAC;cAAe;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAEnE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNjJ,OAAA;UAAKoL,SAAS,EAAC,cAAc;UAAAvC,QAAA,gBACzB7I,OAAA;YAAOkL,IAAI,EAAC,UAAU;YAACW,EAAE,EAAC,YAAY;YAACV,OAAO,EAAEzJ,YAAa;YAACqI,QAAQ,EAAEA,CAAA,KAAMpI,eAAe,CAAC,CAACD,YAAY;UAAE;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChHjJ,OAAA;YAAA6I,QAAA,EAAG;UAIH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC,eACNjJ,OAAA,CAACrC,MAAM;UAACyN,SAAS,EAAC,eAAe;UAACxB,OAAO,EAAE7F,gBAAiB;UAACuI,QAAQ,EAAE,EAAE,CAAC1K,oBAAoB,IAAIF,YAAY,CAAE;UAAAmH,QAAA,EAAC;QAEjH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrC,MAAM;UAACiM,OAAO,EAAE9G,gBAAiB;UAACoG,KAAK,EAAE;YAAEO,UAAU,EAAE;UAAM,CAAE;UAACC,OAAO,EAAC,WAAW;UAACP,KAAK,EAAC,WAAW;UAAAN,QAAA,EAAC;QAEvG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACTjJ,OAAA,CAACpB,MAAM;MAACkN,IAAI,EAAEhK,kBAAmB;MAACiK,OAAO,EAAEhG,iBAAkB;MAAA8C,QAAA,gBACzD7I,OAAA,CAACnB,WAAW;QAAAgK,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzCjJ,OAAA,CAAClB,aAAa;QAAA+J,QAAA,gBACV7I,OAAA,CAAChB,iBAAiB;UAAA6J,QAAA,EAAC;QAEnB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBjJ,OAAA,CAACnC,SAAS;UACN0O,SAAS;UACTC,MAAM,EAAC,OAAO;UACdX,EAAE,EAAC,KAAK;UACRlB,KAAK,EAAC,KAAK;UACXO,IAAI,EAAC,MAAM;UACXuB,SAAS;UACT3G,KAAK,EAAE9D,SAAU;UACjB+H,QAAQ,EAAElE;QAAsB;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAChBjJ,OAAA,CAACjB,aAAa;QAAA8J,QAAA,gBACV7I,OAAA,CAACrC,MAAM;UAACiM,OAAO,EAAE7D,iBAAkB;UAACoD,KAAK,EAAC,SAAS;UAAAN,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrC,MAAM;UAACiM,OAAO,EAAER,YAAa;UAACD,KAAK,EAAC,SAAS;UAAAN,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA,eACX,CAAC;AAGX,CAAC;AAAC5I,EAAA,CAniBWF,WAAW;EAAA,QAEHN,WAAW;AAAA;AAAA6M,EAAA,GAFnBvM,WAAW;AAAA,IAAAuM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}