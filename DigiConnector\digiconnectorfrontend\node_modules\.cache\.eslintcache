[{"E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\index.js": "1", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\App.js": "2", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\AuthContext.jsx": "3", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\reportWebVitals.js": "4", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\app\\store.jsx": "5", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\ResetPasswordPage\\ResetPasswordForm.jsx": "6", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\Login\\Login.jsx": "7", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Home.jsx": "8", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Product\\Productpage.jsx": "9", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\OrderMasterPage.jsx": "10", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Product\\Addproduct\\AddProductPage.jsx": "11", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\OrderPage.jsx": "12", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Channels\\ChannelPage.jsx": "13", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Channels\\ChannelSetting.jsx": "14", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\Register\\Register.jsx": "15", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\OrderDetailsPage.jsx": "16", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Channels\\Channel.jsx": "17", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Channels\\ChannelList.jsx": "18", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\Addorder\\AddNewOrder.jsx": "19", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Jobs\\JobPage.jsx": "20", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Dashboard\\Dashboard.jsx": "21", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Footer\\Footer.jsx": "22", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Navbar\\Navbar.jsx": "23", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Loader\\Loader.jsx": "24", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Product\\Addproduct\\AddProductComponent.jsx": "25", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\app\\features\\cart\\cartSlice.js": "26", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\translate.js": "27", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\SidePanel\\Sidebar.jsx": "28", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Product\\ProductList.jsx": "29", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\orderList.jsx": "30", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Slider.jsx": "31", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Jobs\\JobList.jsx": "32", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\utils\\products.js": "33", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\hooks\\useWindowScrollToTop.jsx": "34", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\api\\products.jsx": "35", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\wrapper\\Wrapper.jsx": "36", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\api\\orders.jsx": "37", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\api\\channel.jsx": "38", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Navbar\\NavigationButton\\LoginNavbar.jsx": "39", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\SliderCard\\SlideCard.jsx": "40", "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\layout\\layout.jsx": "41"}, {"size": 786, "mtime": 1753885774584, "results": "42", "hashOfConfig": "43"}, {"size": 3991, "mtime": 1753895092732, "results": "44", "hashOfConfig": "43"}, {"size": 632, "mtime": 1753894101188, "results": "45", "hashOfConfig": "43"}, {"size": 375, "mtime": 1753885774598, "results": "46", "hashOfConfig": "43"}, {"size": 303, "mtime": 1753893963830, "results": "47", "hashOfConfig": "43"}, {"size": 2800, "mtime": 1753894057352, "results": "48", "hashOfConfig": "43"}, {"size": 3981, "mtime": 1753894002182, "results": "49", "hashOfConfig": "43"}, {"size": 737, "mtime": 1753885774588, "results": "50", "hashOfConfig": "43"}, {"size": 1243, "mtime": 1753941438252, "results": "51", "hashOfConfig": "43"}, {"size": 11713, "mtime": 1753885774592, "results": "52", "hashOfConfig": "43"}, {"size": 14319, "mtime": 1753989902123, "results": "53", "hashOfConfig": "43"}, {"size": 1277, "mtime": 1753885774593, "results": "54", "hashOfConfig": "43"}, {"size": 562, "mtime": 1753941235664, "results": "55", "hashOfConfig": "43"}, {"size": 5256, "mtime": 1753941225471, "results": "56", "hashOfConfig": "43"}, {"size": 6490, "mtime": 1753894019548, "results": "57", "hashOfConfig": "43"}, {"size": 17970, "mtime": 1753885774592, "results": "58", "hashOfConfig": "43"}, {"size": 12225, "mtime": 1753941263854, "results": "59", "hashOfConfig": "43"}, {"size": 29563, "mtime": 1753885774586, "results": "60", "hashOfConfig": "43"}, {"size": 58014, "mtime": 1753992518309, "results": "61", "hashOfConfig": "43"}, {"size": 749, "mtime": 1753941313303, "results": "62", "hashOfConfig": "43"}, {"size": 7491, "mtime": 1753941280562, "results": "63", "hashOfConfig": "43"}, {"size": 1740, "mtime": 1753885774573, "results": "64", "hashOfConfig": "43"}, {"size": 5306, "mtime": 1753885774576, "results": "65", "hashOfConfig": "43"}, {"size": 347, "mtime": 1753885774575, "results": "66", "hashOfConfig": "43"}, {"size": 36919, "mtime": 1753989906343, "results": "67", "hashOfConfig": "43"}, {"size": 2095, "mtime": 1753893962097, "results": "68", "hashOfConfig": "43"}, {"size": 568, "mtime": 1753894111558, "results": "69", "hashOfConfig": "43"}, {"size": 2252, "mtime": 1753885774580, "results": "70", "hashOfConfig": "43"}, {"size": 26680, "mtime": 1753974473633, "results": "71", "hashOfConfig": "43"}, {"size": 11948, "mtime": 1753885774593, "results": "72", "hashOfConfig": "43"}, {"size": 806, "mtime": 1753885774580, "results": "73", "hashOfConfig": "43"}, {"size": 8142, "mtime": 1753941302889, "results": "74", "hashOfConfig": "43"}, {"size": 34182, "mtime": 1753885774599, "results": "75", "hashOfConfig": "43"}, {"size": 146, "mtime": 1753885774583, "results": "76", "hashOfConfig": "43"}, {"size": 3903, "mtime": 1753974503459, "results": "77", "hashOfConfig": "43"}, {"size": 750, "mtime": 1753885774582, "results": "78", "hashOfConfig": "43"}, {"size": 837, "mtime": 1753893888796, "results": "79", "hashOfConfig": "43"}, {"size": 857, "mtime": 1753940719538, "results": "80", "hashOfConfig": "43"}, {"size": 1370, "mtime": 1753885774578, "results": "81", "hashOfConfig": "43"}, {"size": 497, "mtime": 1753885774581, "results": "82", "hashOfConfig": "43"}, {"size": 485, "mtime": 1753894430086, "results": "83", "hashOfConfig": "43"}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jaxqo6", {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\index.js", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\App.js", ["207", "208", "209", "210", "211"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\AuthContext.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\reportWebVitals.js", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\app\\store.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\ResetPasswordPage\\ResetPasswordForm.jsx", ["212", "213", "214"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\Login\\Login.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Home.jsx", ["215", "216"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Product\\Productpage.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\OrderMasterPage.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Product\\Addproduct\\AddProductPage.jsx", ["217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\OrderPage.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Channels\\ChannelPage.jsx", ["232", "233", "234"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Channels\\ChannelSetting.jsx", ["235"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\Register\\Register.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\OrderDetailsPage.jsx", ["236"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Channels\\Channel.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Channels\\ChannelList.jsx", ["237"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\Addorder\\AddNewOrder.jsx", ["238", "239"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Jobs\\JobPage.jsx", ["240"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Dashboard\\Dashboard.jsx", ["241"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Footer\\Footer.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Navbar\\Navbar.jsx", ["242", "243", "244"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Loader\\Loader.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Product\\Addproduct\\AddProductComponent.jsx", ["245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "255"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\app\\features\\cart\\cartSlice.js", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\auth\\translate.js", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\SidePanel\\Sidebar.jsx", ["256", "257", "258", "259", "260", "261", "262", "263"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Product\\ProductList.jsx", ["264", "265", "266", "267", "268", "269", "270"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Order\\orderList.jsx", ["271", "272", "273", "274"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Slider.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\pages\\Jobs\\JobList.jsx", ["275", "276", "277"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\utils\\products.js", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\hooks\\useWindowScrollToTop.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\api\\products.jsx", ["278"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\wrapper\\Wrapper.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\api\\orders.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\api\\channel.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\Navbar\\NavigationButton\\LoginNavbar.jsx", ["279", "280"], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\SliderCard\\SlideCard.jsx", [], [], "E:\\digispin doc\\repos\\logistics\\DigiConnector\\digiconnectorfrontend\\src\\components\\layout\\layout.jsx", ["281"], [], {"ruleId": "282", "severity": 1, "message": "283", "line": 1, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 14}, {"ruleId": "282", "severity": 1, "message": "286", "line": 1, "column": 26, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 34}, {"ruleId": "282", "severity": 1, "message": "287", "line": 4, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 4, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "288", "line": 5, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 5, "endColumn": 16}, {"ruleId": "282", "severity": 1, "message": "289", "line": 23, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 23, "endColumn": 25}, {"ruleId": "282", "severity": 1, "message": "290", "line": 21, "column": 17, "nodeType": "284", "messageId": "285", "endLine": 21, "endColumn": 25}, {"ruleId": "282", "severity": 1, "message": "291", "line": 22, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 22, "endColumn": 17}, {"ruleId": "282", "severity": 1, "message": "292", "line": 22, "column": 19, "nodeType": "284", "messageId": "285", "endLine": 22, "endColumn": 29}, {"ruleId": "282", "severity": 1, "message": "293", "line": 9, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 9, "endColumn": 23}, {"ruleId": "282", "severity": 1, "message": "294", "line": 12, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 12, "endColumn": 18}, {"ruleId": "282", "severity": 1, "message": "295", "line": 6, "column": 46, "nodeType": "284", "messageId": "285", "endLine": 6, "endColumn": 58}, {"ruleId": "282", "severity": 1, "message": "296", "line": 8, "column": 18, "nodeType": "284", "messageId": "285", "endLine": 8, "endColumn": 29}, {"ruleId": "282", "severity": 1, "message": "297", "line": 8, "column": 31, "nodeType": "284", "messageId": "285", "endLine": 8, "endColumn": 39}, {"ruleId": "282", "severity": 1, "message": "298", "line": 11, "column": 18, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 24}, {"ruleId": "282", "severity": 1, "message": "299", "line": 11, "column": 32, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 37}, {"ruleId": "282", "severity": 1, "message": "300", "line": 11, "column": 39, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 48}, {"ruleId": "282", "severity": 1, "message": "301", "line": 11, "column": 50, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 55}, {"ruleId": "282", "severity": 1, "message": "302", "line": 11, "column": 57, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 66}, {"ruleId": "282", "severity": 1, "message": "303", "line": 11, "column": 68, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 77}, {"ruleId": "282", "severity": 1, "message": "304", "line": 11, "column": 79, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 93}, {"ruleId": "282", "severity": 1, "message": "305", "line": 11, "column": 95, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 104}, {"ruleId": "282", "severity": 1, "message": "306", "line": 11, "column": 106, "nodeType": "284", "messageId": "285", "endLine": 11, "endColumn": 114}, {"ruleId": "282", "severity": 1, "message": "307", "line": 17, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 17, "endColumn": 23}, {"ruleId": "282", "severity": 1, "message": "308", "line": 20, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 20, "endColumn": 19}, {"ruleId": "282", "severity": 1, "message": "309", "line": 301, "column": 9, "nodeType": "284", "messageId": "285", "endLine": 301, "endColumn": 25}, {"ruleId": "282", "severity": 1, "message": "286", "line": 1, "column": 17, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 25}, {"ruleId": "282", "severity": 1, "message": "310", "line": 1, "column": 27, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 36}, {"ruleId": "282", "severity": 1, "message": "311", "line": 5, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 5, "endColumn": 21}, {"ruleId": "282", "severity": 1, "message": "312", "line": 83, "column": 19, "nodeType": "284", "messageId": "285", "endLine": 83, "endColumn": 22}, {"ruleId": "282", "severity": 1, "message": "313", "line": 23, "column": 11, "nodeType": "284", "messageId": "285", "endLine": 23, "endColumn": 25}, {"ruleId": "282", "severity": 1, "message": "314", "line": 4, "column": 30, "nodeType": "284", "messageId": "285", "endLine": 4, "endColumn": 40}, {"ruleId": "282", "severity": 1, "message": "315", "line": 31, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 31, "endColumn": 24}, {"ruleId": "282", "severity": 1, "message": "316", "line": 37, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 37, "endColumn": 25}, {"ruleId": "282", "severity": 1, "message": "286", "line": 1, "column": 17, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 25}, {"ruleId": "282", "severity": 1, "message": "317", "line": 4, "column": 8, "nodeType": "284", "messageId": "285", "endLine": 4, "endColumn": 13}, {"ruleId": "318", "severity": 1, "message": "319", "line": 81, "column": 47, "nodeType": "320", "messageId": "321", "endLine": 81, "endColumn": 49}, {"ruleId": "318", "severity": 1, "message": "319", "line": 81, "column": 84, "nodeType": "320", "messageId": "321", "endLine": 81, "endColumn": 86}, {"ruleId": "318", "severity": 1, "message": "319", "line": 81, "column": 189, "nodeType": "320", "messageId": "321", "endLine": 81, "endColumn": 191}, {"ruleId": "282", "severity": 1, "message": "322", "line": 3, "column": 44, "nodeType": "284", "messageId": "285", "endLine": 3, "endColumn": 50}, {"ruleId": "282", "severity": 1, "message": "323", "line": 4, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 4, "endColumn": 19}, {"ruleId": "324", "severity": 1, "message": "325", "line": 26, "column": 21, "nodeType": "326", "endLine": 26, "endColumn": 118}, {"ruleId": "282", "severity": 1, "message": "327", "line": 267, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 267, "endColumn": 26}, {"ruleId": "282", "severity": 1, "message": "328", "line": 267, "column": 28, "nodeType": "284", "messageId": "285", "endLine": 267, "endColumn": 45}, {"ruleId": "282", "severity": 1, "message": "329", "line": 268, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 268, "endColumn": 25}, {"ruleId": "282", "severity": 1, "message": "330", "line": 268, "column": 27, "nodeType": "284", "messageId": "285", "endLine": 268, "endColumn": 43}, {"ruleId": "282", "severity": 1, "message": "331", "line": 271, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 271, "endColumn": 20}, {"ruleId": "282", "severity": 1, "message": "332", "line": 271, "column": 22, "nodeType": "284", "messageId": "285", "endLine": 271, "endColumn": 33}, {"ruleId": "282", "severity": 1, "message": "333", "line": 273, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 273, "endColumn": 31}, {"ruleId": "282", "severity": 1, "message": "334", "line": 273, "column": 33, "nodeType": "284", "messageId": "285", "endLine": 273, "endColumn": 55}, {"ruleId": "282", "severity": 1, "message": "335", "line": 4, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 4, "endColumn": 19}, {"ruleId": "282", "severity": 1, "message": "336", "line": 4, "column": 21, "nodeType": "284", "messageId": "285", "endLine": 4, "endColumn": 24}, {"ruleId": "282", "severity": 1, "message": "337", "line": 4, "column": 26, "nodeType": "284", "messageId": "285", "endLine": 4, "endColumn": 32}, {"ruleId": "282", "severity": 1, "message": "338", "line": 5, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 5, "endColumn": 13}, {"ruleId": "282", "severity": 1, "message": "339", "line": 5, "column": 41, "nodeType": "284", "messageId": "285", "endLine": 5, "endColumn": 57}, {"ruleId": "282", "severity": 1, "message": "340", "line": 5, "column": 77, "nodeType": "284", "messageId": "285", "endLine": 5, "endColumn": 90}, {"ruleId": "282", "severity": 1, "message": "341", "line": 5, "column": 128, "nodeType": "284", "messageId": "285", "endLine": 5, "endColumn": 143}, {"ruleId": "282", "severity": 1, "message": "342", "line": 9, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 9, "endColumn": 31}, {"ruleId": "282", "severity": 1, "message": "310", "line": 1, "column": 27, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 36}, {"ruleId": "282", "severity": 1, "message": "343", "line": 23, "column": 5, "nodeType": "284", "messageId": "285", "endLine": 23, "endColumn": 9}, {"ruleId": "282", "severity": 1, "message": "344", "line": 24, "column": 5, "nodeType": "284", "messageId": "285", "endLine": 24, "endColumn": 13}, {"ruleId": "282", "severity": 1, "message": "345", "line": 50, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 50, "endColumn": 26}, {"ruleId": "318", "severity": 1, "message": "319", "line": 182, "column": 32, "nodeType": "320", "messageId": "321", "endLine": 182, "endColumn": 34}, {"ruleId": "318", "severity": 1, "message": "319", "line": 291, "column": 23, "nodeType": "320", "messageId": "321", "endLine": 291, "endColumn": 25}, {"ruleId": "346", "severity": 1, "message": "347", "line": 537, "column": 29, "nodeType": "326", "endLine": 537, "endColumn": 71}, {"ruleId": "282", "severity": 1, "message": "317", "line": 5, "column": 8, "nodeType": "284", "messageId": "285", "endLine": 5, "endColumn": 13}, {"ruleId": "282", "severity": 1, "message": "348", "line": 6, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 6, "endColumn": 14}, {"ruleId": "282", "severity": 1, "message": "349", "line": 13, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 13, "endColumn": 26}, {"ruleId": "282", "severity": 1, "message": "350", "line": 13, "column": 28, "nodeType": "284", "messageId": "285", "endLine": 13, "endColumn": 45}, {"ruleId": "282", "severity": 1, "message": "351", "line": 1, "column": 38, "nodeType": "284", "messageId": "285", "endLine": 1, "endColumn": 44}, {"ruleId": "282", "severity": 1, "message": "352", "line": 18, "column": 12, "nodeType": "284", "messageId": "285", "endLine": 18, "endColumn": 28}, {"ruleId": "282", "severity": 1, "message": "353", "line": 18, "column": 30, "nodeType": "284", "messageId": "285", "endLine": 18, "endColumn": 49}, {"ruleId": "354", "severity": 1, "message": "355", "line": 45, "column": 13, "nodeType": "356", "messageId": "321", "endLine": 45, "endColumn": 21}, {"ruleId": "282", "severity": 1, "message": "335", "line": 2, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 2, "endColumn": 19}, {"ruleId": "282", "severity": 1, "message": "337", "line": 2, "column": 26, "nodeType": "284", "messageId": "285", "endLine": 2, "endColumn": 32}, {"ruleId": "282", "severity": 1, "message": "287", "line": 3, "column": 10, "nodeType": "284", "messageId": "285", "endLine": 3, "endColumn": 16}, "no-unused-vars", "'lazy' is defined but never used.", "Identifier", "unusedVar", "'useState' is defined but never used.", "'NavBar' is defined but never used.", "'Footer' is defined but never used.", "'ChannelSettings' is defined but never used.", "'setError' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'newArrivalData' is assigned a value but never used.", "'bestSales' is assigned a value but never used.", "'VariantTable' is defined but never used.", "'faArrowLeft' is defined but never used.", "'faUpload' is defined but never used.", "'Dialog' is defined but never used.", "'Paper' is defined but never used.", "'TextField' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'dialogVisible' is assigned a value but never used.", "'openDialog' is assigned a value but never used.", "'handleAddProduct' is assigned a value but never used.", "'useEffect' is defined but never used.", "'ChannelList' is defined but never used.", "'res' is assigned a value but never used.", "'breadCrumItems' is assigned a value but never used.", "'IconButton' is defined but never used.", "'responseData' is assigned a value but never used.", "'paymentMethod' is assigned a value but never used.", "'axios' is defined but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'faEdit' is defined but never used.", "'InputText' is defined but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'variantOptions' is assigned a value but never used.", "'setVariantOptions' is assigned a value but never used.", "'variantValues' is assigned a value but never used.", "'setVariantValues' is assigned a value but never used.", "'variants' is assigned a value but never used.", "'setVariants' is assigned a value but never used.", "'currentVariantIndex' is assigned a value but never used.", "'setCurrentVariantIndex' is assigned a value but never used.", "'Container' is defined but never used.", "'Nav' is defined but never used.", "'Navbar' is defined but never used.", "'bag' is defined but never used.", "'newspaperOutline' is defined but never used.", "'peopleOutline' is defined but never used.", "'settingsOutline' is defined but never used.", "'selectedSidebarItem' is assigned a value but never used.", "'Menu' is defined but never used.", "'MenuItem' is defined but never used.", "'channelDetails' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'read' is defined but never used.", "'selectedOrders' is assigned a value but never used.", "'setSelectedOrders' is assigned a value but never used.", "'useRef' is defined but never used.", "'selectedProducts' is assigned a value but never used.", "'setSelectedProducts' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'quantity'.", "ObjectExpression"]