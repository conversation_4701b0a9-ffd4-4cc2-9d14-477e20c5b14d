const request = require('supertest');
const app = require('../server/app'); // Assuming you have an app.js file
const common = require('../server/constants/common');

// Mock database pool
jest.mock('../server/constants/common');

describe('Inventory Tracking API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /common/api/create_order_with_inventory', () => {
    const mockOrderData = {
      line_items: [
        {
          variant_id: '123',
          quantity: 2,
          sku: 'TEST-001',
          price: 10.00
        },
        {
          variant_id: '124',
          quantity: 1,
          sku: 'TEST-002',
          price: 15.00
        }
      ],
      customer: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        email: '<EMAIL>'
      },
      shipping_address: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        address1: '123 Main St',
        city: 'Anytown',
        province: 'State',
        country: 'Country',
        zip: '12345',
        phone: '555-1234'
      },
      billing_address: {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        address1: '123 Main St',
        city: 'Anytown',
        province: 'State',
        country: 'Country',
        zip: '12345',
        phone: '555-1234'
      },
      email: '<EMAIL>',
      transactions: [
        {
          kind: 'sale',
          status: 'success',
          amount: 35.00
        }
      ],
      financial_status: 'paid'
    };

    test('should create order successfully with sufficient inventory', async () => {
      // Mock user detail
      common.userDetail.mockImplementation((userId, cronName, cronId, callback) => {
        callback({
          success: {
            data: [{ fby_user_id: '1002' }]
          }
        });
      });

      // Mock shopify user detail
      common.shopifyUserDetail.mockImplementation((fbyId, cronName, cronId, callback) => {
        callback({
          success: {
            data: [{
              domain: 'test-shop.myshopify.com',
              token: 'encrypted-token'
            }]
          }
        });
      });

      // Mock inventory validation - sufficient inventory
      common.validateAndUpdateInventory.mockImplementation((variantId, quantity, fbyUserId, callback) => {
        callback({
          success: {
            data: {
              success: true,
              available_quantity: 10,
              message: 'Inventory updated successfully'
            }
          }
        });
      });

      const response = await request(app)
        .post('/common/api/create_order_with_inventory?fby_user_id=1002')
        .send(mockOrderData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Order created successfully with inventory tracking');
      
      // Verify inventory validation was called for each line item
      expect(common.validateAndUpdateInventory).toHaveBeenCalledTimes(2);
      expect(common.validateAndUpdateInventory).toHaveBeenCalledWith('123', 2, '1002', expect.any(Function));
      expect(common.validateAndUpdateInventory).toHaveBeenCalledWith('124', 1, '1002', expect.any(Function));
    });

    test('should fail when insufficient inventory', async () => {
      // Mock user detail
      common.userDetail.mockImplementation((userId, cronName, cronId, callback) => {
        callback({
          success: {
            data: [{ fby_user_id: '1002' }]
          }
        });
      });

      // Mock inventory validation - insufficient inventory
      common.validateAndUpdateInventory.mockImplementation((variantId, quantity, fbyUserId, callback) => {
        callback({
          error: {
            variant_id: variantId,
            available: 1,
            requested: quantity,
            data: 'Insufficient inventory'
          }
        });
      });

      const response = await request(app)
        .post('/common/api/create_order_with_inventory?fby_user_id=1002')
        .send(mockOrderData)
        .expect(400);

      expect(response.body.error).toBe(true);
      expect(response.body.message).toContain('Insufficient inventory');
    });

    test('should handle missing required fields', async () => {
      const incompleteOrderData = {
        line_items: [],
        customer: {}
      };

      const response = await request(app)
        .post('/common/api/create_order_with_inventory?fby_user_id=1002')
        .send(incompleteOrderData)
        .expect(400);

      expect(response.body.error).toBe(true);
    });
  });

  describe('Inventory Validation Functions', () => {
    describe('validateAndUpdateInventory', () => {
      test('should validate and update inventory successfully', async () => {
        const mockResult = [
          [{
            success: true,
            available_quantity: 10,
            requested_quantity: 2,
            message: 'Inventory updated successfully'
          }]
        ];

        // Mock database execution
        const mockExecute = jest.fn((sql, inputs, callback) => {
          callback(null, mockResult, null);
        });

        // Mock dbpool
        const dbpool = { execute: mockExecute };
        common.validateAndUpdateInventory.__setMockDbpool(dbpool);

        const callback = jest.fn();
        await common.validateAndUpdateInventory('123', 2, '1002', callback);

        expect(mockExecute).toHaveBeenCalledWith(
          'CALL validateAndUpdateInventory(?,?,?)',
          ['123', 2, '1002'],
          expect.any(Function)
        );

        expect(callback).toHaveBeenCalledWith({
          success: {
            data: {
              success: true,
              available_quantity: 10,
              requested_quantity: 2,
              message: 'Inventory updated successfully'
            }
          }
        });
      });

      test('should handle insufficient inventory', async () => {
        const mockResult = [
          [{
            success: false,
            available_quantity: 1,
            requested_quantity: 5,
            message: 'Insufficient inventory. Available: 1, Requested: 5'
          }]
        ];

        const mockExecute = jest.fn((sql, inputs, callback) => {
          callback(null, mockResult, null);
        });

        const dbpool = { execute: mockExecute };
        common.validateAndUpdateInventory.__setMockDbpool(dbpool);

        const callback = jest.fn();
        await common.validateAndUpdateInventory('123', 5, '1002', callback);

        expect(callback).toHaveBeenCalledWith({
          error: {
            variant_id: '123',
            available: 1,
            requested: 5,
            data: 'Insufficient inventory. Available: 1, Requested: 5'
          }
        });
      });

      test('should handle database errors', async () => {
        const mockError = new Error('Database connection failed');

        const mockExecute = jest.fn((sql, inputs, callback) => {
          callback(mockError, null, null);
        });

        const dbpool = { execute: mockExecute };
        common.validateAndUpdateInventory.__setMockDbpool(dbpool);

        const callback = jest.fn();
        await common.validateAndUpdateInventory('123', 2, '1002', callback);

        expect(callback).toHaveBeenCalledWith({
          error: {
            variant_id: '123',
            data: 'Database connection failed'
          }
        });
      });
    });

    describe('updateInventoryQuantity', () => {
      test('should update inventory quantity successfully', async () => {
        const mockResult = [
          [{
            variant_id: '123',
            previous_quantity: 10,
            current_quantity: 8,
            quantity_change: -2,
            message: 'Inventory updated successfully'
          }]
        ];

        const mockExecute = jest.fn((sql, inputs, callback) => {
          callback(null, mockResult, null);
        });

        const dbpool = { execute: mockExecute };
        common.updateInventoryQuantity.__setMockDbpool(dbpool);

        const callback = jest.fn();
        await common.updateInventoryQuantity('123', -2, '1002', callback);

        expect(mockExecute).toHaveBeenCalledWith(
          'CALL updateInventoryQuantity(?,?,?)',
          ['123', -2, '1002'],
          expect.any(Function)
        );

        expect(callback).toHaveBeenCalledWith({
          success: {
            data: {
              variant_id: '123',
              previous_quantity: 10,
              current_quantity: 8,
              quantity_change: -2,
              message: 'Inventory updated successfully'
            }
          }
        });
      });
    });
  });

  describe('Database Stored Procedures', () => {
    test('validateAndUpdateInventory procedure should work correctly', () => {
      // This would be an integration test that actually calls the database
      // For now, we'll just verify the SQL structure is correct
      const expectedSQL = 'CALL validateAndUpdateInventory(?,?,?)';
      const expectedParams = ['variant_id', 'requested_quantity', 'fby_user_id'];
      
      expect(expectedSQL).toBe('CALL validateAndUpdateInventory(?,?,?)');
      expect(expectedParams).toHaveLength(3);
    });

    test('updateInventoryQuantity procedure should work correctly', () => {
      const expectedSQL = 'CALL updateInventoryQuantity(?,?,?)';
      const expectedParams = ['variant_id', 'quantity_change', 'fby_user_id'];
      
      expect(expectedSQL).toBe('CALL updateInventoryQuantity(?,?,?)');
      expect(expectedParams).toHaveLength(3);
    });
  });

  describe('Error Scenarios', () => {
    test('should handle concurrent inventory updates', async () => {
      // Mock scenario where inventory is updated between validation and order creation
      common.validateAndUpdateInventory
        .mockImplementationOnce((variantId, quantity, fbyUserId, callback) => {
          callback({
            success: {
              data: {
                success: true,
                available_quantity: 5,
                message: 'Inventory updated successfully'
              }
            }
          });
        })
        .mockImplementationOnce((variantId, quantity, fbyUserId, callback) => {
          callback({
            error: {
              variant_id: variantId,
              available: 0,
              requested: quantity,
              data: 'Insufficient inventory - concurrent update'
            }
          });
        });

      const response = await request(app)
        .post('/common/api/create_order_with_inventory?fby_user_id=1002')
        .send(mockOrderData)
        .expect(400);

      expect(response.body.error).toBe(true);
      expect(response.body.message).toContain('concurrent update');
    });

    test('should rollback on partial failure', async () => {
      // This test would verify that if one item fails inventory validation,
      // the entire order creation is rolled back
      // Implementation would depend on transaction handling in the actual code
    });
  });
});
