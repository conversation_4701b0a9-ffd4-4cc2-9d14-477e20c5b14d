-- Stored procedure for validating and updating inventory
DROP PROCEDURE IF EXISTS `validateAndUpdateInventory`;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `validateAndUpdateInventory`(
    IN `variant_id` VARCHAR(127),
    IN `requested_quantity` INT,
    IN `fby_user_id` VARCHAR(128)
)
BEGIN
    DECLARE current_quantity INT DEFAULT 0;
    DECLARE success_flag BOOLEAN DEFAULT FALSE;
    DECLARE error_message VARCHAR(255) DEFAULT '';
    
    -- Get current inventory quantity
    SELECT inventory_quantity INTO current_quantity
    FROM createdproductvariants 
    WHERE id = variant_id AND fby_user_id = fby_user_id
    LIMIT 1;
    
    -- Check if we have sufficient inventory
    IF current_quantity >= requested_quantity THEN
        -- Update inventory by reducing the requested quantity
        UPDATE createdproductvariants 
        SET 
            previous_inventory_quantity = inventory_quantity,
            inventory_quantity = inventory_quantity - requested_quantity,
            updated_at = NOW()
        WHERE id = variant_id AND fby_user_id = fby_user_id;
        
        SET success_flag = TRUE;
        SET error_message = 'Inventory updated successfully';
    ELSE
        SET success_flag = FALSE;
        SET error_message = CONCAT('Insufficient inventory. Available: ', current_quantity, ', Requested: ', requested_quantity);
    END IF;
    
    -- Return result
    SELECT 
        success_flag as success,
        current_quantity as available_quantity,
        requested_quantity as requested_quantity,
        error_message as message;
        
END ;;
DELIMITER ;

-- Stored procedure for updating inventory quantity
DROP PROCEDURE IF EXISTS `updateInventoryQuantity`;

DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `updateInventoryQuantity`(
    IN `variant_id` VARCHAR(127),
    IN `quantity_change` INT,
    IN `fby_user_id` VARCHAR(128)
)
BEGIN
    DECLARE current_quantity INT DEFAULT 0;
    DECLARE new_quantity INT DEFAULT 0;
    
    -- Get current inventory quantity
    SELECT inventory_quantity INTO current_quantity
    FROM createdproductvariants 
    WHERE id = variant_id AND fby_user_id = fby_user_id
    LIMIT 1;
    
    -- Calculate new quantity
    SET new_quantity = current_quantity + quantity_change;
    
    -- Ensure quantity doesn't go below 0
    IF new_quantity < 0 THEN
        SET new_quantity = 0;
    END IF;
    
    -- Update inventory
    UPDATE createdproductvariants 
    SET 
        previous_inventory_quantity = inventory_quantity,
        inventory_quantity = new_quantity,
        updated_at = NOW()
    WHERE id = variant_id AND fby_user_id = fby_user_id;
    
    -- Return result
    SELECT 
        variant_id,
        current_quantity as previous_quantity,
        new_quantity as current_quantity,
        quantity_change,
        'Inventory updated successfully' as message;
        
END ;;
DELIMITER ;
