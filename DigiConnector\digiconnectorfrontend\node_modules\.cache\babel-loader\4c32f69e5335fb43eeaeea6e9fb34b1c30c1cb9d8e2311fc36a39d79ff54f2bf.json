{"ast": null, "code": "var _jsxFileName = \"E:\\\\digispin doc\\\\repos\\\\logistics\\\\DigiConnector\\\\digiconnectorfrontend\\\\src\\\\pages\\\\Product\\\\Addproduct\\\\AddProductPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { NavBar } from '../../../components/Navbar/Navbar';\nimport { Sidebar } from '../../../components/SidePanel/Sidebar.jsx';\nimport { MediaComponent, InventoryComponent, VariantTable, OrganizeAndClassifyComponent, WeightAndDimensionsComponent, VariantsComponent, TitleAndDescriptionComponent } from './AddProductComponent.jsx';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faSave, faArrowLeft, faUpload } from '@fortawesome/free-solid-svg-icons';\nimport './addProductPage.css';\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\nimport { Button, Dialog, Grid, Paper, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, DialogTitle } from '@mui/material'; // Import Material-UI components\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const AddProductPage = () => {\n  _s();\n  const {\n    productId\n  } = useParams();\n  const navigate = useNavigate();\n  const [dialogVisible, setDialogVisible] = useState(false); // Define dialog visibility state\n\n  // Define openDialog function\n  const openDialog = () => {\n    setDialogVisible(true);\n  };\n  const [product, setProduct] = useState({\n    newProduct: {\n      fields: {\n        title: '',\n        tags: [''],\n        brand: '',\n        weight: {\n          unit: 'kg',\n          value: ''\n        },\n        dimensions: {\n          unit: 'm',\n          width: '',\n          height: '',\n          length: ''\n        },\n        short_description: '',\n        description: '',\n        categories: [''],\n        price: '',\n        quantity: '',\n        sku: '',\n        barcode: '',\n        asin: '',\n        product_type: 'Test Product'\n      },\n      photos: ['https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG'],\n      variants: [],\n      options: [{\n        name: 'Size',\n        values: ['S', 'M', 'L']\n      }, {\n        name: 'Color',\n        values: ['Red', 'Blue', 'Green']\n      }],\n      isUpdate: 'no'\n    }\n  });\n  useEffect(() => {\n    if (productId) {\n      fetchProductDetails(productId);\n    }\n  }, [productId]);\n  const fetchProductDetails = async productId => {\n    try {\n      const response = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_product/?fby_user_id=8&sku=${productId}`, {\n        isCreated: true\n      }, {\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n        }\n      });\n      const newProduct = response.data.newProduct;\n      setProduct({\n        newProduct: {\n          fields: {\n            title: newProduct.fields.title || '',\n            tags: Array.isArray(newProduct.fields.tags) ? newProduct.fields.tags : [newProduct.fields.tags || ''],\n            brand: newProduct.fields.brand || '',\n            price: newProduct.fields.price || '',\n            quantity: newProduct.fields.inventory_quantity || '',\n            sku: newProduct.fields.sku || '',\n            barcode: newProduct.fields.barcode || '',\n            asin: newProduct.fields.asin || '',\n            product_type: newProduct.fields.product_type || 'Test Product',\n            weight: {\n              value: newProduct.fields.weight_value || '',\n              unit: newProduct.fields.weight_unit || 'kg'\n            },\n            dimensions: {\n              unit: newProduct.fields.dimensions_unit || 'm',\n              width: newProduct.fields.dimensions_width || '',\n              height: newProduct.fields.dimensions_height || '',\n              length: newProduct.fields.dimensions_length || ''\n            },\n            short_description: newProduct.fields.description || '',\n            description: newProduct.fields.description || '',\n            categories: Array.isArray(newProduct.fields.categories) ? newProduct.fields.categories : [newProduct.fields.category || '']\n          },\n          photos: newProduct.photos ? newProduct.photos.map(photo => photo.image || photo) : ['https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG'],\n          variants: newProduct.variants ? newProduct.variants.map(variant => ({\n            id: variant.id,\n            sku: variant.sku,\n            barcode: variant.barcode,\n            item_id: variant.item_id,\n            title: variant.title,\n            inventory_quantity: variant.inventory_quantity || 0,\n            image: variant.image,\n            price: variant.price || 0,\n            specialPrice: variant.specialPrice,\n            skuFamily: variant.skuFamily,\n            position: variant.position || 1,\n            option1: variant.option1 || '',\n            option2: variant.option2 || ''\n          })) : [],\n          options: newProduct.options && Array.isArray(newProduct.options) ? newProduct.options : [{\n            name: 'Size',\n            values: ['S', 'M', 'L']\n          }, {\n            name: 'Color',\n            values: ['Red', 'Blue', 'Green']\n          }],\n          isUpdate: productId ? 'yes' : 'no'\n        }\n      });\n      console.log(newProduct);\n    } catch (error) {\n      console.error('Error fetching product details:', error);\n    }\n  };\n  const validateProduct = () => {\n    const errors = [];\n    const fields = product.newProduct.fields;\n    if (!fields.title || fields.title.trim() === '') {\n      errors.push('Product title is required');\n    }\n    if (!fields.sku || fields.sku.trim() === '') {\n      errors.push('SKU is required');\n    }\n    if (!fields.price || parseFloat(fields.price) <= 0) {\n      errors.push('Valid price is required');\n    }\n    if (!fields.quantity || parseInt(fields.quantity) < 0) {\n      errors.push('Valid quantity is required');\n    }\n    return errors;\n  };\n  const handleSave = async () => {\n    try {\n      // Validate product data\n      const validationErrors = validateProduct();\n      if (validationErrors.length > 0) {\n        alert('Please fix the following errors:\\n' + validationErrors.join('\\n'));\n        return;\n      }\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\n      if (!storedGroupCode) {\n        alert('Group code not found. Please login again.');\n        return;\n      }\n      let url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}`;\n      if (productId) {\n        url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}&isUpdate=yes`;\n      }\n      console.log('Sending product data:', JSON.stringify(product, null, 2));\n      console.log('Product fields:', product.newProduct.fields);\n      console.log('SKU being sent:', product.newProduct.fields.sku);\n      console.log('Base quantity being sent:', product.newProduct.fields.quantity);\n      console.log('Base price being sent:', product.newProduct.fields.price);\n\n      // Log variant details for debugging\n      if (product.newProduct.variants && product.newProduct.variants.length > 0) {\n        console.log('=== VARIANT DETAILS ===');\n        product.newProduct.variants.forEach((variant, index) => {\n          console.log(`Variant ${index + 1}:`, {\n            title: variant.title,\n            sku: variant.sku,\n            price: variant.price,\n            quantity: variant.quantity,\n            inventory_quantity: variant.inventory_quantity,\n            barcode: variant.barcode\n          });\n        });\n        console.log('=== END VARIANT DETAILS ===');\n      }\n      const res = await axios({\n        url: url,\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n        },\n        data: [product]\n      });\n      console.log('Response:', res);\n      if (res.data && res.data.success) {\n        alert('Product saved successfully!');\n      } else {\n        alert('Product saved, but there might be some issues. Check console for details.');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error saving product:', error);\n      alert('Error saving product: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message));\n    }\n  };\n  const handleFieldChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === 'short_description' || name === 'description') {\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            [name]: value,\n            // Also update the other description field to keep them in sync\n            [name === 'short_description' ? 'description' : 'short_description']: value\n          }\n        }\n      }));\n    } else if (name === 'categories') {\n      // Handle categories as array\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            categories: value.split(',').map(cat => cat.trim()).filter(cat => cat)\n          }\n        }\n      }));\n    } else if (name === 'tags') {\n      // Handle tags as array\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            tags: value.split(',').map(tag => tag.trim()).filter(tag => tag)\n          }\n        }\n      }));\n    } else if (name.startsWith('weight.')) {\n      // Handle weight object properties\n      const weightProp = name.split('.')[1];\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            weight: {\n              ...prevProduct.newProduct.fields.weight,\n              [weightProp]: value\n            }\n          }\n        }\n      }));\n    } else if (name.startsWith('dimensions.')) {\n      // Handle dimensions object properties\n      const dimensionProp = name.split('.')[1];\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            dimensions: {\n              ...prevProduct.newProduct.fields.dimensions,\n              [dimensionProp]: value\n            }\n          }\n        }\n      }));\n    } else {\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          fields: {\n            ...prevProduct.newProduct.fields,\n            [name]: value\n          }\n        }\n      }));\n    }\n  };\n  const handleImageChange = file => {\n    // Upload image file and update the product's photos state\n    // This function will vary depending on your backend implementation\n    // For example:\n    // const formData = new FormData();\n    // formData.append('image', file);\n    // axios.post('/upload', formData).then((response) => {\n    //    const imageUrl = response.data.imageUrl;\n    //    setProduct((prevProduct) => ({\n    //      ...prevProduct,\n    //      newProduct: {\n    //        ...prevProduct.newProduct,\n    //        photos: [...prevProduct.newProduct.photos, imageUrl],\n    //      },\n    //    }));\n    // });\n  };\n  const handleAddProduct = variants => {\n    setProduct(prevProduct => ({\n      ...prevProduct,\n      newProduct: {\n        ...prevProduct.newProduct,\n        variants: variants\n      }\n    }));\n  };\n  const handleVariantChange = (arg1, arg2, arg3) => {\n    if (Array.isArray(arg1)) {\n      // If the first argument is an array, assume it's an array of updatedVariants\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          variants: arg1\n        }\n      }));\n      setProduct(prevProduct => ({\n        ...prevProduct,\n        newProduct: {\n          ...prevProduct.newProduct,\n          options: arg2\n        }\n      }));\n    } else {\n      // If the first argument is not an array, assume individual parameters (index, property, value)\n      const index = arg1;\n      const property = arg2;\n      const value = arg3;\n      setProduct(prevProduct => {\n        const updatedVariants = [...prevProduct.newProduct.variants];\n        updatedVariants[index][property] = value;\n        return {\n          ...prevProduct,\n          newProduct: {\n            ...prevProduct.newProduct,\n            variants: updatedVariants\n          }\n        };\n      });\n    }\n  };\n  const handleBack = () => {\n    navigate(-1); // Go back to previous page\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(NavBar, {\n      selectedSidebarItem: \"products\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header\",\n        children: [/*#__PURE__*/_jsxDEV(ArrowBackIcon, {\n          onClick: handleBack\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: /*#__PURE__*/_jsxDEV(\"b\", {\n            children: \"Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 24\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSave\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 87\n          }, this),\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TitleAndDescriptionComponent, {\n            product: product,\n            handleFieldChange: handleFieldChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(MediaComponent, {\n            product: product,\n            handleImageChange: handleImageChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(InventoryComponent, {\n            product: product,\n            handleFieldChange: handleFieldChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(VariantsComponent, {\n            product: product,\n            handleVariantChange: handleVariantChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(OrganizeAndClassifyComponent, {\n            product: product,\n            handleFieldChange: handleFieldChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(WeightAndDimensionsComponent, {\n            product: product,\n            handleFieldChange: handleFieldChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddProductPage, \"T3raoHFLyM7gigeX0C+dTXfHFLg=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = AddProductPage;\nvar _c;\n$RefreshReg$(_c, \"AddProductPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useParams", "useNavigate", "NavBar", "Sidebar", "MediaComponent", "InventoryComponent", "VariantTable", "OrganizeAndClassifyComponent", "WeightAndDimensionsComponent", "VariantsComponent", "TitleAndDescriptionComponent", "FontAwesomeIcon", "faSave", "faArrowLeft", "faUpload", "ArrowBackIcon", "<PERSON><PERSON>", "Dialog", "Grid", "Paper", "TextField", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "DialogTitle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddProductPage", "_s", "productId", "navigate", "dialogVisible", "setDialogVisible", "openDialog", "product", "setProduct", "newProduct", "fields", "title", "tags", "brand", "weight", "unit", "value", "dimensions", "width", "height", "length", "short_description", "description", "categories", "price", "quantity", "sku", "barcode", "asin", "product_type", "photos", "variants", "options", "name", "values", "isUpdate", "fetchProductDetails", "response", "post", "process", "env", "REACT_APP_BASE_URL", "isCreated", "headers", "Authorization", "REACT_APP_ACCESS_TOKEN", "data", "Array", "isArray", "inventory_quantity", "weight_value", "weight_unit", "dimensions_unit", "dimensions_width", "dimensions_height", "dimensions_length", "category", "map", "photo", "image", "variant", "id", "item_id", "specialPrice", "skuFamily", "position", "option1", "option2", "console", "log", "error", "validateProduct", "errors", "trim", "push", "parseFloat", "parseInt", "handleSave", "validationErrors", "alert", "join", "storedGroupCode", "localStorage", "getItem", "url", "JSON", "stringify", "for<PERSON>ach", "index", "res", "method", "success", "_error$response", "_error$response$data", "message", "handleFieldChange", "e", "target", "prevProduct", "split", "cat", "filter", "tag", "startsWith", "weightProp", "dimensionProp", "handleImageChange", "file", "handleAddProduct", "handleVariantChange", "arg1", "arg2", "arg3", "property", "updatedVariants", "handleBack", "children", "selectedSidebarItem", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "color", "startIcon", "icon", "container", "spacing", "item", "xs", "_c", "$RefreshReg$"], "sources": ["E:/digispin doc/repos/logistics/DigiConnector/digiconnectorfrontend/src/pages/Product/Addproduct/AddProductPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport { NavBar } from '../../../components/Navbar/Navbar';\r\nimport { Sidebar } from '../../../components/SidePanel/Sidebar.jsx';\r\nimport { MediaComponent, InventoryComponent, VariantTable, OrganizeAndClassifyComponent, WeightAndDimensionsComponent, VariantsComponent, TitleAndDescriptionComponent } from './AddProductComponent.jsx';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faSave, faArrowLeft, faUpload } from '@fortawesome/free-solid-svg-icons';\r\nimport './addProductPage.css';\r\nimport ArrowBackIcon from '@mui/icons-material/ArrowBack';\r\nimport { Button, Dialog, Grid, Paper, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, DialogTitle } from '@mui/material'; // Import Material-UI components\r\n\r\n\r\nexport const AddProductPage = () => {\r\n  const { productId } = useParams();\r\n  const navigate = useNavigate();\r\n  const [dialogVisible, setDialogVisible] = useState(false); // Define dialog visibility state\r\n\r\n  // Define openDialog function\r\n  const openDialog = () => {\r\n    setDialogVisible(true);\r\n  };\r\n\r\n  const [product, setProduct] = useState({\r\n    newProduct: {\r\n      fields: {\r\n        title: '',\r\n        tags: [''],\r\n        brand: '',\r\n        weight: { unit: 'kg', value: '' },\r\n        dimensions: { unit: 'm', width: '', height: '', length: '' },\r\n        short_description: '',\r\n        description: '',\r\n        categories: [''],\r\n        price: '',\r\n        quantity: '',\r\n        sku: '',\r\n        barcode: '',\r\n        asin: '',\r\n        product_type: 'Test Product'\r\n      },\r\n      photos: ['https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG'],\r\n      variants: [],\r\n      options: [\r\n        { name: 'Size', values: ['S', 'M', 'L'] },\r\n        { name: 'Color', values: ['Red', 'Blue', 'Green'] }\r\n      ],\r\n      isUpdate: 'no',\r\n    },\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (productId) {\r\n      fetchProductDetails(productId);\r\n    }\r\n  }, [productId]);\r\n\r\n  const fetchProductDetails = async (productId) => {\r\n    try {\r\n      const response = await axios.post(\r\n        `${process.env.REACT_APP_BASE_URL}/common/api/get_product/?fby_user_id=8&sku=${productId}`,\r\n        {\r\n          isCreated: true,\r\n        },\r\n        {\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n            Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n          },\r\n        }\r\n      );\r\n      const newProduct = response.data.newProduct;\r\n      setProduct({\r\n        newProduct: {\r\n          fields: {\r\n            title: newProduct.fields.title || '',\r\n            tags: Array.isArray(newProduct.fields.tags) ? newProduct.fields.tags : [newProduct.fields.tags || ''],\r\n            brand: newProduct.fields.brand || '',\r\n            price: newProduct.fields.price || '',\r\n            quantity: newProduct.fields.inventory_quantity || '',\r\n            sku: newProduct.fields.sku || '',\r\n            barcode: newProduct.fields.barcode || '',\r\n            asin: newProduct.fields.asin || '',\r\n            product_type: newProduct.fields.product_type || 'Test Product',\r\n            weight: {\r\n              value: newProduct.fields.weight_value || '',\r\n              unit: newProduct.fields.weight_unit || 'kg'\r\n            },\r\n            dimensions: {\r\n              unit: newProduct.fields.dimensions_unit || 'm',\r\n              width: newProduct.fields.dimensions_width || '',\r\n              height: newProduct.fields.dimensions_height || '',\r\n              length: newProduct.fields.dimensions_length || '',\r\n            },\r\n            short_description: newProduct.fields.description || '',\r\n            description: newProduct.fields.description || '',\r\n            categories: Array.isArray(newProduct.fields.categories) ? newProduct.fields.categories : [newProduct.fields.category || ''],\r\n          },\r\n          photos: newProduct.photos ? newProduct.photos.map(photo => photo.image || photo) : ['https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG'],\r\n          variants: newProduct.variants ? newProduct.variants.map(variant => ({\r\n            id: variant.id,\r\n            sku: variant.sku,\r\n            barcode: variant.barcode,\r\n            item_id: variant.item_id,\r\n            title: variant.title,\r\n            inventory_quantity: variant.inventory_quantity || 0,\r\n            image: variant.image,\r\n            price: variant.price || 0,\r\n            specialPrice: variant.specialPrice,\r\n            skuFamily: variant.skuFamily,\r\n            position: variant.position || 1,\r\n            option1: variant.option1 || '',\r\n            option2: variant.option2 || ''\r\n          })) : [],\r\n          options: newProduct.options && Array.isArray(newProduct.options) ? newProduct.options : [\r\n            { name: 'Size', values: ['S', 'M', 'L'] },\r\n            { name: 'Color', values: ['Red', 'Blue', 'Green'] }\r\n          ],\r\n          isUpdate: productId ? 'yes' : 'no',\r\n        },\r\n      });\r\n      console.log(newProduct);\r\n\r\n    } catch (error) {\r\n      console.error('Error fetching product details:', error);\r\n    }\r\n  };\r\n\r\n  const validateProduct = () => {\r\n    const errors = [];\r\n    const fields = product.newProduct.fields;\r\n\r\n    if (!fields.title || fields.title.trim() === '') {\r\n      errors.push('Product title is required');\r\n    }\r\n    if (!fields.sku || fields.sku.trim() === '') {\r\n      errors.push('SKU is required');\r\n    }\r\n    if (!fields.price || parseFloat(fields.price) <= 0) {\r\n      errors.push('Valid price is required');\r\n    }\r\n    if (!fields.quantity || parseInt(fields.quantity) < 0) {\r\n      errors.push('Valid quantity is required');\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    try {\r\n      // Validate product data\r\n      const validationErrors = validateProduct();\r\n      if (validationErrors.length > 0) {\r\n        alert('Please fix the following errors:\\n' + validationErrors.join('\\n'));\r\n        return;\r\n      }\r\n\r\n      var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n      if (!storedGroupCode) {\r\n        alert('Group code not found. Please login again.');\r\n        return;\r\n      }\r\n\r\n      let url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}`;\r\n      if (productId) {\r\n        url = `${process.env.REACT_APP_BASE_URL}/common/api/push_shopify_product/?fby_user_id=${storedGroupCode}&sku=${product.newProduct.fields.sku}&isUpdate=yes`;\r\n      }\r\n\r\n      console.log('Sending product data:', JSON.stringify(product, null, 2));\r\n      console.log('Product fields:', product.newProduct.fields);\r\n      console.log('SKU being sent:', product.newProduct.fields.sku);\r\n      console.log('Base quantity being sent:', product.newProduct.fields.quantity);\r\n      console.log('Base price being sent:', product.newProduct.fields.price);\r\n\r\n      // Log variant details for debugging\r\n      if (product.newProduct.variants && product.newProduct.variants.length > 0) {\r\n        console.log('=== VARIANT DETAILS ===');\r\n        product.newProduct.variants.forEach((variant, index) => {\r\n          console.log(`Variant ${index + 1}:`, {\r\n            title: variant.title,\r\n            sku: variant.sku,\r\n            price: variant.price,\r\n            quantity: variant.quantity,\r\n            inventory_quantity: variant.inventory_quantity,\r\n            barcode: variant.barcode\r\n          });\r\n        });\r\n        console.log('=== END VARIANT DETAILS ===');\r\n      }\r\n\r\n      const res = await axios({\r\n        url: url,\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n        },\r\n        data: [product],\r\n      });\r\n\r\n      console.log('Response:', res);\r\n      if (res.data && res.data.success) {\r\n        alert('Product saved successfully!');\r\n      } else {\r\n        alert('Product saved, but there might be some issues. Check console for details.');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving product:', error);\r\n      alert('Error saving product: ' + (error.response?.data?.message || error.message));\r\n    }\r\n  };\r\n\r\n  const handleFieldChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === 'short_description' || name === 'description') {\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            [name]: value,\r\n            // Also update the other description field to keep them in sync\r\n            [name === 'short_description' ? 'description' : 'short_description']: value,\r\n          },\r\n        },\r\n      }));\r\n    } else if (name === 'categories') {\r\n      // Handle categories as array\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            categories: value.split(',').map(cat => cat.trim()).filter(cat => cat),\r\n          },\r\n        },\r\n      }));\r\n    } else if (name === 'tags') {\r\n      // Handle tags as array\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            tags: value.split(',').map(tag => tag.trim()).filter(tag => tag),\r\n          },\r\n        },\r\n      }));\r\n    } else if (name.startsWith('weight.')) {\r\n      // Handle weight object properties\r\n      const weightProp = name.split('.')[1];\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            weight: {\r\n              ...prevProduct.newProduct.fields.weight,\r\n              [weightProp]: value,\r\n            },\r\n          },\r\n        },\r\n      }));\r\n    } else if (name.startsWith('dimensions.')) {\r\n      // Handle dimensions object properties\r\n      const dimensionProp = name.split('.')[1];\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            dimensions: {\r\n              ...prevProduct.newProduct.fields.dimensions,\r\n              [dimensionProp]: value,\r\n            },\r\n          },\r\n        },\r\n      }));\r\n    } else {\r\n      setProduct((prevProduct) => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          fields: {\r\n            ...prevProduct.newProduct.fields,\r\n            [name]: value,\r\n          },\r\n        },\r\n      }));\r\n    }\r\n  };\r\n\r\n\r\n  const handleImageChange = (file) => {\r\n    // Upload image file and update the product's photos state\r\n    // This function will vary depending on your backend implementation\r\n    // For example:\r\n    // const formData = new FormData();\r\n    // formData.append('image', file);\r\n    // axios.post('/upload', formData).then((response) => {\r\n    //    const imageUrl = response.data.imageUrl;\r\n    //    setProduct((prevProduct) => ({\r\n    //      ...prevProduct,\r\n    //      newProduct: {\r\n    //        ...prevProduct.newProduct,\r\n    //        photos: [...prevProduct.newProduct.photos, imageUrl],\r\n    //      },\r\n    //    }));\r\n    // });\r\n  };\r\n\r\n  const handleAddProduct = (variants) => {\r\n    setProduct((prevProduct) => ({\r\n      ...prevProduct,\r\n      newProduct: {\r\n        ...prevProduct.newProduct,\r\n        variants: variants,\r\n      },\r\n    }));\r\n  };\r\n\r\n  const handleVariantChange = (arg1, arg2, arg3) => {\r\n    if (Array.isArray(arg1)) {\r\n      // If the first argument is an array, assume it's an array of updatedVariants\r\n      setProduct(prevProduct => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          variants: arg1\r\n        },\r\n      }));\r\n      setProduct(prevProduct => ({\r\n        ...prevProduct,\r\n        newProduct: {\r\n          ...prevProduct.newProduct,\r\n          options: arg2\r\n        },\r\n      }));\r\n    } else {\r\n      // If the first argument is not an array, assume individual parameters (index, property, value)\r\n      const index = arg1;\r\n      const property = arg2;\r\n      const value = arg3;\r\n\r\n      setProduct(prevProduct => {\r\n        const updatedVariants = [...prevProduct.newProduct.variants];\r\n        updatedVariants[index][property] = value;\r\n\r\n        return {\r\n          ...prevProduct,\r\n          newProduct: {\r\n            ...prevProduct.newProduct,\r\n            variants: updatedVariants,\r\n          },\r\n        };\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleBack = () => {\r\n    navigate(-1); // Go back to previous page\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <NavBar selectedSidebarItem=\"products\" />\r\n      <Sidebar />\r\n      <div className=\"page-content\">\r\n        <div className=\"header\">\r\n          {/* <Button onClick={handleBack} variant=\"contained\" startIcon={<FontAwesomeIcon icon={faArrowLeft} />}>\r\n            Back\r\n          </Button> */}\r\n          <ArrowBackIcon onClick={handleBack} />\r\n          <DialogTitle><b>Product</b></DialogTitle>\r\n          <Button onClick={handleSave} variant=\"contained\" color=\"primary\" startIcon={<FontAwesomeIcon icon={faSave} />}>\r\n            Save\r\n          </Button>\r\n        </div>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <TitleAndDescriptionComponent product={product} handleFieldChange={handleFieldChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <MediaComponent product={product} handleImageChange={handleImageChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            {/* Inventory component */}\r\n            <InventoryComponent product={product} handleFieldChange={handleFieldChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <VariantsComponent product={product} handleVariantChange={handleVariantChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <OrganizeAndClassifyComponent product={product} handleFieldChange={handleFieldChange} />\r\n          </Grid>\r\n        </Grid>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12}>\r\n            <WeightAndDimensionsComponent product={product} handleFieldChange={handleFieldChange} />\r\n          </Grid>\r\n        </Grid>\r\n      </div>\r\n    </>\r\n  );\r\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,OAAO,QAAQ,2CAA2C;AACnE,SAASC,cAAc,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,4BAA4B,EAAEC,4BAA4B,EAAEC,iBAAiB,EAAEC,4BAA4B,QAAQ,2BAA2B;AACzM,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,mCAAmC;AACjF,OAAO,sBAAsB;AAC7B,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,eAAe,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGvJ,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAU,CAAC,GAAGlC,SAAS,CAAC,CAAC;EACjC,MAAMmC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE3D;EACA,MAAMyC,UAAU,GAAGA,CAAA,KAAM;IACvBD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC;IACrC4C,UAAU,EAAE;MACVC,MAAM,EAAE;QACNC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;UAAEC,IAAI,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAG,CAAC;QACjCC,UAAU,EAAE;UAAEF,IAAI,EAAE,GAAG;UAAEG,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAG,CAAC;QAC5DC,iBAAiB,EAAE,EAAE;QACrBC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE,CAAC,EAAE,CAAC;QAChBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,GAAG,EAAE,EAAE;QACPC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE;MAChB,CAAC;MACDC,MAAM,EAAE,CAAC,gGAAgG,CAAC;MAC1GC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,CACP;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;MAAE,CAAC,EACzC;QAAED,IAAI,EAAE,OAAO;QAAEC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO;MAAE,CAAC,CACpD;MACDC,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC;EAEFrE,SAAS,CAAC,MAAM;IACd,IAAIoC,SAAS,EAAE;MACbkC,mBAAmB,CAAClC,SAAS,CAAC;IAChC;EACF,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAMkC,mBAAmB,GAAG,MAAOlC,SAAS,IAAK;IAC/C,IAAI;MACF,MAAMmC,QAAQ,GAAG,MAAMtE,KAAK,CAACuE,IAAI,CAC/B,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,8CAA8CvC,SAAS,EAAE,EAC1F;QACEwC,SAAS,EAAE;MACb,CAAC,EACD;QACEC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;QAC7D;MACF,CACF,CAAC;MACD,MAAMpC,UAAU,GAAG4B,QAAQ,CAACS,IAAI,CAACrC,UAAU;MAC3CD,UAAU,CAAC;QACTC,UAAU,EAAE;UACVC,MAAM,EAAE;YACNC,KAAK,EAAEF,UAAU,CAACC,MAAM,CAACC,KAAK,IAAI,EAAE;YACpCC,IAAI,EAAEmC,KAAK,CAACC,OAAO,CAACvC,UAAU,CAACC,MAAM,CAACE,IAAI,CAAC,GAAGH,UAAU,CAACC,MAAM,CAACE,IAAI,GAAG,CAACH,UAAU,CAACC,MAAM,CAACE,IAAI,IAAI,EAAE,CAAC;YACrGC,KAAK,EAAEJ,UAAU,CAACC,MAAM,CAACG,KAAK,IAAI,EAAE;YACpCW,KAAK,EAAEf,UAAU,CAACC,MAAM,CAACc,KAAK,IAAI,EAAE;YACpCC,QAAQ,EAAEhB,UAAU,CAACC,MAAM,CAACuC,kBAAkB,IAAI,EAAE;YACpDvB,GAAG,EAAEjB,UAAU,CAACC,MAAM,CAACgB,GAAG,IAAI,EAAE;YAChCC,OAAO,EAAElB,UAAU,CAACC,MAAM,CAACiB,OAAO,IAAI,EAAE;YACxCC,IAAI,EAAEnB,UAAU,CAACC,MAAM,CAACkB,IAAI,IAAI,EAAE;YAClCC,YAAY,EAAEpB,UAAU,CAACC,MAAM,CAACmB,YAAY,IAAI,cAAc;YAC9Df,MAAM,EAAE;cACNE,KAAK,EAAEP,UAAU,CAACC,MAAM,CAACwC,YAAY,IAAI,EAAE;cAC3CnC,IAAI,EAAEN,UAAU,CAACC,MAAM,CAACyC,WAAW,IAAI;YACzC,CAAC;YACDlC,UAAU,EAAE;cACVF,IAAI,EAAEN,UAAU,CAACC,MAAM,CAAC0C,eAAe,IAAI,GAAG;cAC9ClC,KAAK,EAAET,UAAU,CAACC,MAAM,CAAC2C,gBAAgB,IAAI,EAAE;cAC/ClC,MAAM,EAAEV,UAAU,CAACC,MAAM,CAAC4C,iBAAiB,IAAI,EAAE;cACjDlC,MAAM,EAAEX,UAAU,CAACC,MAAM,CAAC6C,iBAAiB,IAAI;YACjD,CAAC;YACDlC,iBAAiB,EAAEZ,UAAU,CAACC,MAAM,CAACY,WAAW,IAAI,EAAE;YACtDA,WAAW,EAAEb,UAAU,CAACC,MAAM,CAACY,WAAW,IAAI,EAAE;YAChDC,UAAU,EAAEwB,KAAK,CAACC,OAAO,CAACvC,UAAU,CAACC,MAAM,CAACa,UAAU,CAAC,GAAGd,UAAU,CAACC,MAAM,CAACa,UAAU,GAAG,CAACd,UAAU,CAACC,MAAM,CAAC8C,QAAQ,IAAI,EAAE;UAC5H,CAAC;UACD1B,MAAM,EAAErB,UAAU,CAACqB,MAAM,GAAGrB,UAAU,CAACqB,MAAM,CAAC2B,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACC,KAAK,IAAID,KAAK,CAAC,GAAG,CAAC,gGAAgG,CAAC;UACrL3B,QAAQ,EAAEtB,UAAU,CAACsB,QAAQ,GAAGtB,UAAU,CAACsB,QAAQ,CAAC0B,GAAG,CAACG,OAAO,KAAK;YAClEC,EAAE,EAAED,OAAO,CAACC,EAAE;YACdnC,GAAG,EAAEkC,OAAO,CAAClC,GAAG;YAChBC,OAAO,EAAEiC,OAAO,CAACjC,OAAO;YACxBmC,OAAO,EAAEF,OAAO,CAACE,OAAO;YACxBnD,KAAK,EAAEiD,OAAO,CAACjD,KAAK;YACpBsC,kBAAkB,EAAEW,OAAO,CAACX,kBAAkB,IAAI,CAAC;YACnDU,KAAK,EAAEC,OAAO,CAACD,KAAK;YACpBnC,KAAK,EAAEoC,OAAO,CAACpC,KAAK,IAAI,CAAC;YACzBuC,YAAY,EAAEH,OAAO,CAACG,YAAY;YAClCC,SAAS,EAAEJ,OAAO,CAACI,SAAS;YAC5BC,QAAQ,EAAEL,OAAO,CAACK,QAAQ,IAAI,CAAC;YAC/BC,OAAO,EAAEN,OAAO,CAACM,OAAO,IAAI,EAAE;YAC9BC,OAAO,EAAEP,OAAO,CAACO,OAAO,IAAI;UAC9B,CAAC,CAAC,CAAC,GAAG,EAAE;UACRnC,OAAO,EAAEvB,UAAU,CAACuB,OAAO,IAAIe,KAAK,CAACC,OAAO,CAACvC,UAAU,CAACuB,OAAO,CAAC,GAAGvB,UAAU,CAACuB,OAAO,GAAG,CACtF;YAAEC,IAAI,EAAE,MAAM;YAAEC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UAAE,CAAC,EACzC;YAAED,IAAI,EAAE,OAAO;YAAEC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO;UAAE,CAAC,CACpD;UACDC,QAAQ,EAAEjC,SAAS,GAAG,KAAK,GAAG;QAChC;MACF,CAAC,CAAC;MACFkE,OAAO,CAACC,GAAG,CAAC5D,UAAU,CAAC;IAEzB,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAM9D,MAAM,GAAGH,OAAO,CAACE,UAAU,CAACC,MAAM;IAExC,IAAI,CAACA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAAC8D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/CD,MAAM,CAACE,IAAI,CAAC,2BAA2B,CAAC;IAC1C;IACA,IAAI,CAAChE,MAAM,CAACgB,GAAG,IAAIhB,MAAM,CAACgB,GAAG,CAAC+C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3CD,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACA,IAAI,CAAChE,MAAM,CAACc,KAAK,IAAImD,UAAU,CAACjE,MAAM,CAACc,KAAK,CAAC,IAAI,CAAC,EAAE;MAClDgD,MAAM,CAACE,IAAI,CAAC,yBAAyB,CAAC;IACxC;IACA,IAAI,CAAChE,MAAM,CAACe,QAAQ,IAAImD,QAAQ,CAAClE,MAAM,CAACe,QAAQ,CAAC,GAAG,CAAC,EAAE;MACrD+C,MAAM,CAACE,IAAI,CAAC,4BAA4B,CAAC;IAC3C;IAEA,OAAOF,MAAM;EACf,CAAC;EAED,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,MAAMC,gBAAgB,GAAGP,eAAe,CAAC,CAAC;MAC1C,IAAIO,gBAAgB,CAAC1D,MAAM,GAAG,CAAC,EAAE;QAC/B2D,KAAK,CAAC,oCAAoC,GAAGD,gBAAgB,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;QACzE;MACF;MAEA,IAAIC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACvD,IAAI,CAACF,eAAe,EAAE;QACpBF,KAAK,CAAC,2CAA2C,CAAC;QAClD;MACF;MAEA,IAAIK,GAAG,GAAG,GAAG7C,OAAO,CAACC,GAAG,CAACC,kBAAkB,iDAAiDwC,eAAe,QAAQ1E,OAAO,CAACE,UAAU,CAACC,MAAM,CAACgB,GAAG,EAAE;MAClJ,IAAIxB,SAAS,EAAE;QACbkF,GAAG,GAAG,GAAG7C,OAAO,CAACC,GAAG,CAACC,kBAAkB,iDAAiDwC,eAAe,QAAQ1E,OAAO,CAACE,UAAU,CAACC,MAAM,CAACgB,GAAG,eAAe;MAC7J;MAEA0C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,IAAI,CAACC,SAAS,CAAC/E,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACtE6D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE9D,OAAO,CAACE,UAAU,CAACC,MAAM,CAAC;MACzD0D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE9D,OAAO,CAACE,UAAU,CAACC,MAAM,CAACgB,GAAG,CAAC;MAC7D0C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE9D,OAAO,CAACE,UAAU,CAACC,MAAM,CAACe,QAAQ,CAAC;MAC5E2C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE9D,OAAO,CAACE,UAAU,CAACC,MAAM,CAACc,KAAK,CAAC;;MAEtE;MACA,IAAIjB,OAAO,CAACE,UAAU,CAACsB,QAAQ,IAAIxB,OAAO,CAACE,UAAU,CAACsB,QAAQ,CAACX,MAAM,GAAG,CAAC,EAAE;QACzEgD,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtC9D,OAAO,CAACE,UAAU,CAACsB,QAAQ,CAACwD,OAAO,CAAC,CAAC3B,OAAO,EAAE4B,KAAK,KAAK;UACtDpB,OAAO,CAACC,GAAG,CAAC,WAAWmB,KAAK,GAAG,CAAC,GAAG,EAAE;YACnC7E,KAAK,EAAEiD,OAAO,CAACjD,KAAK;YACpBe,GAAG,EAAEkC,OAAO,CAAClC,GAAG;YAChBF,KAAK,EAAEoC,OAAO,CAACpC,KAAK;YACpBC,QAAQ,EAAEmC,OAAO,CAACnC,QAAQ;YAC1BwB,kBAAkB,EAAEW,OAAO,CAACX,kBAAkB;YAC9CtB,OAAO,EAAEiC,OAAO,CAACjC;UACnB,CAAC,CAAC;QACJ,CAAC,CAAC;QACFyC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;MAEA,MAAMoB,GAAG,GAAG,MAAM1H,KAAK,CAAC;QACtBqH,GAAG,EAAEA,GAAG;QACRM,MAAM,EAAE,MAAM;QACd/C,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUL,OAAO,CAACC,GAAG,CAACK,sBAAsB;QAC7D,CAAC;QACDC,IAAI,EAAE,CAACvC,OAAO;MAChB,CAAC,CAAC;MAEF6D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoB,GAAG,CAAC;MAC7B,IAAIA,GAAG,CAAC3C,IAAI,IAAI2C,GAAG,CAAC3C,IAAI,CAAC6C,OAAO,EAAE;QAChCZ,KAAK,CAAC,6BAA6B,CAAC;MACtC,CAAC,MAAM;QACLA,KAAK,CAAC,2EAA2E,CAAC;MACpF;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAsB,eAAA,EAAAC,oBAAA;MACdzB,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CS,KAAK,CAAC,wBAAwB,IAAI,EAAAa,eAAA,GAAAtB,KAAK,CAACjC,QAAQ,cAAAuD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB9C,IAAI,cAAA+C,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAIxB,KAAK,CAACwB,OAAO,CAAC,CAAC;IACpF;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAE/D,IAAI;MAAEjB;IAAM,CAAC,GAAGgF,CAAC,CAACC,MAAM;IAChC,IAAIhE,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,aAAa,EAAE;MAC1DzB,UAAU,CAAE0F,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdzF,UAAU,EAAE;UACV,GAAGyF,WAAW,CAACzF,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGwF,WAAW,CAACzF,UAAU,CAACC,MAAM;YAChC,CAACuB,IAAI,GAAGjB,KAAK;YACb;YACA,CAACiB,IAAI,KAAK,mBAAmB,GAAG,aAAa,GAAG,mBAAmB,GAAGjB;UACxE;QACF;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIiB,IAAI,KAAK,YAAY,EAAE;MAChC;MACAzB,UAAU,CAAE0F,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdzF,UAAU,EAAE;UACV,GAAGyF,WAAW,CAACzF,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGwF,WAAW,CAACzF,UAAU,CAACC,MAAM;YAChCa,UAAU,EAAEP,KAAK,CAACmF,KAAK,CAAC,GAAG,CAAC,CAAC1C,GAAG,CAAC2C,GAAG,IAAIA,GAAG,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC4B,MAAM,CAACD,GAAG,IAAIA,GAAG;UACvE;QACF;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAInE,IAAI,KAAK,MAAM,EAAE;MAC1B;MACAzB,UAAU,CAAE0F,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdzF,UAAU,EAAE;UACV,GAAGyF,WAAW,CAACzF,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGwF,WAAW,CAACzF,UAAU,CAACC,MAAM;YAChCE,IAAI,EAAEI,KAAK,CAACmF,KAAK,CAAC,GAAG,CAAC,CAAC1C,GAAG,CAAC6C,GAAG,IAAIA,GAAG,CAAC7B,IAAI,CAAC,CAAC,CAAC,CAAC4B,MAAM,CAACC,GAAG,IAAIA,GAAG;UACjE;QACF;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIrE,IAAI,CAACsE,UAAU,CAAC,SAAS,CAAC,EAAE;MACrC;MACA,MAAMC,UAAU,GAAGvE,IAAI,CAACkE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrC3F,UAAU,CAAE0F,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdzF,UAAU,EAAE;UACV,GAAGyF,WAAW,CAACzF,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGwF,WAAW,CAACzF,UAAU,CAACC,MAAM;YAChCI,MAAM,EAAE;cACN,GAAGoF,WAAW,CAACzF,UAAU,CAACC,MAAM,CAACI,MAAM;cACvC,CAAC0F,UAAU,GAAGxF;YAChB;UACF;QACF;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIiB,IAAI,CAACsE,UAAU,CAAC,aAAa,CAAC,EAAE;MACzC;MACA,MAAME,aAAa,GAAGxE,IAAI,CAACkE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxC3F,UAAU,CAAE0F,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdzF,UAAU,EAAE;UACV,GAAGyF,WAAW,CAACzF,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGwF,WAAW,CAACzF,UAAU,CAACC,MAAM;YAChCO,UAAU,EAAE;cACV,GAAGiF,WAAW,CAACzF,UAAU,CAACC,MAAM,CAACO,UAAU;cAC3C,CAACwF,aAAa,GAAGzF;YACnB;UACF;QACF;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLR,UAAU,CAAE0F,WAAW,KAAM;QAC3B,GAAGA,WAAW;QACdzF,UAAU,EAAE;UACV,GAAGyF,WAAW,CAACzF,UAAU;UACzBC,MAAM,EAAE;YACN,GAAGwF,WAAW,CAACzF,UAAU,CAACC,MAAM;YAChC,CAACuB,IAAI,GAAGjB;UACV;QACF;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAGD,MAAM0F,iBAAiB,GAAIC,IAAI,IAAK;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;EAED,MAAMC,gBAAgB,GAAI7E,QAAQ,IAAK;IACrCvB,UAAU,CAAE0F,WAAW,KAAM;MAC3B,GAAGA,WAAW;MACdzF,UAAU,EAAE;QACV,GAAGyF,WAAW,CAACzF,UAAU;QACzBsB,QAAQ,EAAEA;MACZ;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM8E,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IAChD,IAAIjE,KAAK,CAACC,OAAO,CAAC8D,IAAI,CAAC,EAAE;MACvB;MACAtG,UAAU,CAAC0F,WAAW,KAAK;QACzB,GAAGA,WAAW;QACdzF,UAAU,EAAE;UACV,GAAGyF,WAAW,CAACzF,UAAU;UACzBsB,QAAQ,EAAE+E;QACZ;MACF,CAAC,CAAC,CAAC;MACHtG,UAAU,CAAC0F,WAAW,KAAK;QACzB,GAAGA,WAAW;QACdzF,UAAU,EAAE;UACV,GAAGyF,WAAW,CAACzF,UAAU;UACzBuB,OAAO,EAAE+E;QACX;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACA,MAAMvB,KAAK,GAAGsB,IAAI;MAClB,MAAMG,QAAQ,GAAGF,IAAI;MACrB,MAAM/F,KAAK,GAAGgG,IAAI;MAElBxG,UAAU,CAAC0F,WAAW,IAAI;QACxB,MAAMgB,eAAe,GAAG,CAAC,GAAGhB,WAAW,CAACzF,UAAU,CAACsB,QAAQ,CAAC;QAC5DmF,eAAe,CAAC1B,KAAK,CAAC,CAACyB,QAAQ,CAAC,GAAGjG,KAAK;QAExC,OAAO;UACL,GAAGkF,WAAW;UACdzF,UAAU,EAAE;YACV,GAAGyF,WAAW,CAACzF,UAAU;YACzBsB,QAAQ,EAAEmF;UACZ;QACF,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBhH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,oBACEN,OAAA,CAAAE,SAAA;IAAAqH,QAAA,gBACEvH,OAAA,CAAC3B,MAAM;MAACmJ,mBAAmB,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzC5H,OAAA,CAAC1B,OAAO;MAAAmJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX5H,OAAA;MAAK6H,SAAS,EAAC,cAAc;MAAAN,QAAA,gBAC3BvH,OAAA;QAAK6H,SAAS,EAAC,QAAQ;QAAAN,QAAA,gBAIrBvH,OAAA,CAACd,aAAa;UAAC4I,OAAO,EAAER;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtC5H,OAAA,CAACF,WAAW;UAAAyH,QAAA,eAACvH,OAAA;YAAAuH,QAAA,EAAG;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzC5H,OAAA,CAACb,MAAM;UAAC2I,OAAO,EAAE9C,UAAW;UAACjB,OAAO,EAAC,WAAW;UAACgE,KAAK,EAAC,SAAS;UAACC,SAAS,eAAEhI,OAAA,CAAClB,eAAe;YAACmJ,IAAI,EAAElJ;UAAO;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAAC;QAE/G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN5H,OAAA,CAACX,IAAI;QAAC6I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzBvH,OAAA,CAACX,IAAI;UAAC+I,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChBvH,OAAA,CAACnB,4BAA4B;YAAC6B,OAAO,EAAEA,OAAQ;YAACwF,iBAAiB,EAAEA;UAAkB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5H,OAAA,CAACX,IAAI;QAAC6I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzBvH,OAAA,CAACX,IAAI;UAAC+I,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChBvH,OAAA,CAACzB,cAAc;YAACmC,OAAO,EAAEA,OAAQ;YAACmG,iBAAiB,EAAEA;UAAkB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5H,OAAA,CAACX,IAAI;QAAC6I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzBvH,OAAA,CAACX,IAAI;UAAC+I,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAEhBvH,OAAA,CAACxB,kBAAkB;YAACkC,OAAO,EAAEA,OAAQ;YAACwF,iBAAiB,EAAEA;UAAkB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5H,OAAA,CAACX,IAAI;QAAC6I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzBvH,OAAA,CAACX,IAAI;UAAC+I,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChBvH,OAAA,CAACpB,iBAAiB;YAAC8B,OAAO,EAAEA,OAAQ;YAACsG,mBAAmB,EAAEA;UAAoB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5H,OAAA,CAACX,IAAI;QAAC6I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzBvH,OAAA,CAACX,IAAI;UAAC+I,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChBvH,OAAA,CAACtB,4BAA4B;YAACgC,OAAO,EAAEA,OAAQ;YAACwF,iBAAiB,EAAEA;UAAkB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5H,OAAA,CAACX,IAAI;QAAC6I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,eACzBvH,OAAA,CAACX,IAAI;UAAC+I,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAChBvH,OAAA,CAACrB,4BAA4B;YAAC+B,OAAO,EAAEA,OAAQ;YAACwF,iBAAiB,EAAEA;UAAkB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACxH,EAAA,CApZWD,cAAc;EAAA,QACHhC,SAAS,EACdC,WAAW;AAAA;AAAAkK,EAAA,GAFjBnI,cAAc;AAAA,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}