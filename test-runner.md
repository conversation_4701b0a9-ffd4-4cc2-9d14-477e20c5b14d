# Test Runner Guide for Order Creation with Inventory Tracking

## Overview
This guide explains how to run the comprehensive tests for the improved order creation functionality with inventory tracking.

## Frontend Tests

### Setup
1. Navigate to the frontend directory:
   ```bash
   cd DigiConnector/digiconnectorfrontend
   ```

2. Install testing dependencies (if not already installed):
   ```bash
   npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event jest-environment-jsdom
   ```

3. Add test script to package.json:
   ```json
   {
     "scripts": {
       "test": "react-scripts test",
       "test:coverage": "react-scripts test --coverage --watchAll=false"
     }
   }
   ```

### Running Frontend Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- --testPathPattern=OrderCreation.test.js
```

### Test Coverage Areas
- Product browsing and grouping by base product name
- Quantity input validation against available inventory
- Real-time inventory display updates
- Order creation with inventory tracking
- Error handling for insufficient stock
- Order summary calculations

## Backend Tests

### Setup
1. Navigate to the backend directory:
   ```bash
   cd logistics_backend
   ```

2. Install testing dependencies:
   ```bash
   npm install --save-dev jest supertest
   ```

3. Add test configuration to package.json:
   ```json
   {
     "scripts": {
       "test": "jest",
       "test:watch": "jest --watch",
       "test:coverage": "jest --coverage"
     },
     "jest": {
       "testEnvironment": "node",
       "collectCoverageFrom": [
         "server/**/*.js",
         "!server/node_modules/**"
       ]
     }
   }
   ```

### Running Backend Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- inventoryTracking.test.js
```

### Test Coverage Areas
- API endpoint for order creation with inventory tracking
- Inventory validation and update functions
- Database stored procedures for inventory management
- Error handling for insufficient inventory
- Concurrent inventory update scenarios
- Transaction rollback on failures

## Database Tests

### Setup Database for Testing
1. Create a test database:
   ```sql
   CREATE DATABASE channelconnector_test;
   ```

2. Import the schema with the new stored procedures:
   ```bash
   mysql -u root -p channelconnector_test < db1.sql
   ```

3. Insert test data:
   ```sql
   INSERT INTO createdproductvariants (id, fby_user_id, sku, title, inventory_quantity, price) VALUES
   (1, '1002', 'TEST-001', 'Test Product 1', 10, 15.00),
   (2, '1002', 'TEST-002', 'Test Product 2', 5, 20.00),
   (3, '1002', 'TEST-003', 'Test Product 3', 0, 25.00);
   ```

### Manual Testing Scenarios

#### Scenario 1: Successful Order Creation
1. Browse products and select items with sufficient inventory
2. Set quantities within available limits
3. Create order and verify inventory is reduced

#### Scenario 2: Insufficient Inventory
1. Try to order more items than available
2. Verify error message is displayed
3. Confirm order is not created

#### Scenario 3: Real-time Inventory Updates
1. Select products and observe inventory display
2. Change quantities and verify "after selection" values
3. Confirm order summary updates correctly

#### Scenario 4: Product Grouping
1. Browse products with same base names
2. Verify products are grouped by skuFamily
3. Confirm variants are displayed correctly

## Integration Testing

### End-to-End Test Flow
1. Start both frontend and backend servers
2. Use a tool like Cypress or Playwright for E2E tests
3. Test complete order creation workflow:
   - Login
   - Navigate to order creation
   - Browse and select products
   - Verify inventory tracking
   - Create order
   - Confirm inventory reduction

### Performance Testing
- Test with large product catalogs (1000+ products)
- Verify grouping performance
- Test concurrent order creation
- Monitor database query performance

## Test Data Management

### Sample Test Data
```javascript
const testProducts = [
  {
    id: 1,
    title: 'T-Shirt Red Large',
    sku: 'TSHIRT-RED-L',
    skuFamily: 'TSHIRT',
    price: 19.99,
    inventory_quantity: 15,
    option_1_value: 'Red',
    option_2_value: 'Large'
  },
  {
    id: 2,
    title: 'T-Shirt Red Medium',
    sku: 'TSHIRT-RED-M',
    skuFamily: 'TSHIRT',
    price: 19.99,
    inventory_quantity: 8,
    option_1_value: 'Red',
    option_2_value: 'Medium'
  }
];
```

### Database Cleanup
After each test, ensure database state is reset:
```sql
DELETE FROM orders WHERE test_order = 1;
UPDATE createdproductvariants SET inventory_quantity = original_quantity WHERE test_product = 1;
```

## Continuous Integration

### GitHub Actions Example
```yaml
name: Test Order Creation
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm test
      - run: npm run test:coverage
```

## Troubleshooting

### Common Issues
1. **Database connection errors**: Ensure test database is running
2. **Mock failures**: Verify all external dependencies are properly mocked
3. **Async test issues**: Use proper async/await or done callbacks
4. **Environment variables**: Ensure test environment variables are set

### Debug Tips
- Use `console.log` for debugging test data
- Run tests individually to isolate issues
- Check network requests in browser dev tools
- Verify database state before and after tests
