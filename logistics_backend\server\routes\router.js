const express = require("express");
const homeRoutes = require("../services/render.js").homeRoutes;
const shopifyController = require("../controller/shopifyController.js");
const router = express.Router();
const auth = require("../../middleware/auth");
const upload = require("../../middleware/upload");

const commonApiController = require("../controller/commonApiController");
const errorManageController = require("../controller/errorManageController");
const storedenController = require("../controller/storedenController.js");
const woocommerceController = require("../controller/woocommerceController.js");
const amazonMwsController = require("../controller/amazonMwsController");
const amazonSPApiController = require("../controller/amazonSPAPIController");
const miraklController = require("../controller/miraklControler");
const fbyController = require("../controller/fbyController");
const magentoController = require("../controller/magentoController.js");

//common controllers
const common = require("../constants/common.js");
const commonController = require("../controller/commonController.js");
const authController = require('../controller/authController.js');
const roleController = require('../controller/roleController.js');
const organizationController = require('../controller/organizationController.js');

//hcl cotrollers
const dashboardController = require('../controller/hcl/dashboardController');
const hclOrderController = require("../controller/hcl/orderController.js");
const paymentController = require('../controller/hcl/paymentController.js');
const warehouseController = require('../controller/hcl/warehouseController.js');
const statusController = require('../controller/hcl/statusControllers.js');
const clientController = require('../controller/hcl/clientController.js');
const pincodeController = require('../controller/hcl/pincodeController.js');
const walletController = require('../controller/hcl/walletController.js');
const zoneController = require('../controller/hcl/zoneController.js');
const weightSlabController = require('../controller/hcl/weightSlabController.js');
const clientsRatesController = require('../controller/hcl/clientsRatesController.js');
const shipmentsController = require('../controller/hcl/shipmentsController.js');
const codPayoutController = require('../controller/hcl/codPayoutController.js');
const manifestController = require('../controller/hcl/manifestController.js');
const shippingProviderController = require('../controller/hcl/shippingProviderController.js');
const shippingProviderWeightsController = require('../controller/hcl/shippingProviderWeightsController.js');
const shippingProviderRatesController = require('../controller/hcl/shippingProviderRatesController.js');


/**
 *@description Root Route when the server starts
 *
 */
router.get("/", homeRoutes);

router.get("/shopify/api/get_shopify_products", auth.checkMultiAuthorization, shopifyController.getShopifyProducts);

router.get("/shopify/api/get_shopify_location", auth.checkMultiAuthorization, shopifyController.getShopifyLocation);

router.get("/shopify/api/send_products_fby", auth.checkMultiAuthorization, shopifyController.sendProductsFby);

router.get("/shopify/api/get_fby_stock", auth.checkMultiAuthorization, shopifyController.getFbyStock);

router.get("/shopify/api/push_stock_shopify", auth.checkMultiAuthorization, shopifyController.pushStockShopify);

router.get("/shopify/api/get_shopify_orders", auth.checkMultiAuthorization, shopifyController.getShopifyOrders);

router.get("/shopify/api/send_orders_fby", auth.checkMultiAuthorization, shopifyController.sendOrdersFby);

router.get("/shopify/api/send_cancelled_orders_fby", auth.checkMultiAuthorization, shopifyController.sendCanceledOrdersFby);

router.get("/shopify/api/get_track_number", auth.checkMultiAuthorization, shopifyController.getFbyTraknumber);

router.get("/shopify/api/push_tracks_shopify", auth.checkMultiAuthorization, shopifyController.pushTrackShopify);

router.get("/shopify/api/error_manage", auth.checkMultiAuthorization, errorManageController.errorManager);

router.get("/api/fby_apis", auth.checkMultiAuthorization, commonApiController.fbyCommonApis);

router.get("/api/fby_alert", auth.checkMultiAuthorization, fbyController.sendAlertToFBY);

router.get("/api/get_cron_schedule", fbyController.getCronSchedule);

router.get("/api/get_logs_by_alert_id", fbyController.getLogsByAlertId);

//storeden starts
//http://localhost:3000/storeden/api/get_products_list?userid=1000
router.get("/storeden/api/get_products_list", auth.checkMultiAuthorization, storedenController.getStoredenProducts);

//Not used in current flow
router.get("/storeden/api/add_product_storeden", auth.checkMultiAuthorization, storedenController.sendProductsStoreden);

//Not used in current flow
router.get("/storeden/api/get_stock", auth.checkMultiAuthorization, storedenController.getStoredenStock);

//http://localhost:3000/storeden/api/push_stock_storeden?fby_user_id=1000
router.get("/storeden/api/push_stock_storeden", auth.checkMultiAuthorization, storedenController.pushStockStoreden);

//http://localhost:3000/storeden/api/get_storeden_orders?fby_user_id=1000
router.get("/storeden/api/get_storeden_orders", auth.checkMultiAuthorization, storedenController.getStoredenOrders);

//Not used in current flow
router.get("/storeden/api/get_track_number", auth.checkMultiAuthorization, storedenController.getStoredenTrackNumber);

//http://localhost:3000/storeden/api/push_track?fby_user_id=1000
router.get("/storeden/api/push_track", auth.checkMultiAuthorization, storedenController.pushTrackStoreden);
//storeden ends

//WooCommerce starts
//http://localhost:3000/woocommerce/api/get_products_list?userid=1006
router.get("/woocommerce/api/get_products_list", auth.checkMultiAuthorization, woocommerceController.getWooCommerceProducts);

//http://localhost:3000/woocommerce/api/push_stock_woocommerce?fby_user_id=1006
router.get("/woocommerce/api/push_stock_woocommerce", auth.checkMultiAuthorization, woocommerceController.pushStockWoocommerce);

//http://localhost:3000/woocommerce/api/get_woocommerce_orders?fby_user_id=1006
router.get("/woocommerce/api/get_woocommerce_orders", auth.checkMultiAuthorization, woocommerceController.getWoocommerceOrders);

//http://localhost:3000/woocommerce/api/push_track?fby_user_id=1000
router.get("/woocommerce/api/push_track", auth.checkMultiAuthorization, woocommerceController.pushTrackWoocommerce);
//woocommerce ends


//amazonMws starts
//http://localhost:3000/amazonMws/api/getOrders
//router.get("/amazonMws/api/getOrders", amazonMwsController.getWooCommerceProducts);
//amazonMws ends

//#region Product and price sync from FBY to Shopify
/*
1
GET_PRODUCTS_FROM_FBY_TIMER
http://localhost:3000/shopify/api/get_products_fby?fby_user_id=8
*/
router.get("/shopify/api/get_products_fby", auth.checkMultiAuthorization, shopifyController.getFbyProducts);

/*
2
GET_PRODUCTS_PRICE_FROM_FBY_TIMER
http://localhost:3000/shopify/api/get_prices_fby?fby_user_id=8
*/
router.get("/shopify/api/get_prices_fby", auth.checkMultiAuthorization, shopifyController.getFbyProductPrices);

/*
3
PUSH_PRODUCTS_TO_SHOPIFY_TIMER
http://localhost:3000/shopify/api/push_product_shopify?fby_user_id=8
*/
router.get("/shopify/api/push_product_shopify", auth.checkMultiAuthorization, shopifyController.createProductsShopify);

/*
4
PUSH_PRODUCTS_IMAGES_TO_SHOPIFY_TIMER
http://localhost:3000/shopify/api/push_product_images?fby_user_id=8
*/
router.get("/shopify/api/push_product_images", auth.checkMultiAuthorization, shopifyController.pushImagesShopify);

/*
5
PUSH_PRODUCTS_VARIANTS_TO_SHOPIFY_TIMER
http://localhost:3000/shopify/api/push_variants_shopify?fby_user_id=8
*/
router.get("/shopify/api/push_variants_shopify", auth.checkMultiAuthorization, shopifyController.createProductVariantShopify);


/*
6
PUSH_PRODUCTS_VARIANTS_TO_SHOPIFY_TIMER
http://localhost:3000/shopify/api/push_variants_shopify?fby_user_id=8
*/
router.get("/shopify/api/update_products_shopify", auth.checkMultiAuthorization, shopifyController.updateProductsOrVariantsShopify);
//#endregion

//Amazon step 1
router.get("/amazon/api/generate_Products_Report_Amazon", auth.checkMultiAuthorization, amazonSPApiController.generateAmazonProductsReport);

//Amazon step 2
router.get("/amazon/api/get_Products_Amazon", auth.checkMultiAuthorization, amazonSPApiController.getAmazonProducts);

//Amazon step 3
router.get("/amazon/api/get_Orders_Amazon", auth.checkMultiAuthorization, amazonSPApiController.getAmazonOrders);

router.get("/amazon/api/push_stock_Amazon", amazonSPApiController.pushStockAmazon);

router.get("/amazon/api/push_Tracking_Amazon", amazonSPApiController.pushTrackAmazon);


//http://localhost:3000/amazon/api/push_track?fby_user_id=1011
router.get("/amazon/api/push_track", amazonSPApiController.pushTrackAmazon);
//amazon ends


//Mirakl Begins
router.get("/mirakl/api/get_Products_Mirakl", auth.checkMultiAuthorization, miraklController.getmiraklProducts);

router.get("/mirakl/api/get_carriers_Mirakl", auth.checkMultiAuthorization, miraklController.getmiraklCarriers);

router.get("/mirakl/api/push_stock_Mirakl", auth.checkMultiAuthorization, miraklController.pushStockmirakl);

router.get("/mirakl/api/get_Orders_Mirakl", auth.checkMultiAuthorization, miraklController.getmiraklOrders);

router.get("/mirakl/api/push_Tracking_Mirakl", auth.checkMultiAuthorization, miraklController.pushTrackmirakl);

/*
1
GET_PRODUCTS_PRICE_FROM_FBY_TIMER
http://localhost:3000/fby/api/get_prices_fby?fby_user_id=8
*/
router.get("/fby/api/get_prices_fby", auth.checkMultiAuthorization, amazonSPApiController.getFbyProductPrices);


/*
2
PUSH_PRICE_DETAILS_TO_AMAZON_TIMER
http://localhost:3000/amazon/api/push_price?fby_user_id=8
*/
router.get("/amazon/api/push_price", auth.checkMultiAuthorization, amazonSPApiController.pushPriceAmazon);

router.get('/magento/api/get_magento_products', auth.checkMultiAuthorization, magentoController.getMagentoProducts);

router.get('/magento/api/push_stock_magento', auth.checkMultiAuthorization, magentoController.pushStockMagento);

/* order route */
router.get('/magento/api/get_magento_orders', auth.checkMultiAuthorization, magentoController.getMagentoOrders);

router.get('/magento/api/push_traks_magento', auth.checkMultiAuthorization, magentoController.pushTrackMagento);

router.get('/magento/api/push_traks_magento', auth.checkMultiAuthorization, magentoController.pushTrackMagento);



/* ************************************The below are all apis for DigiHub Project********************** */

// Login Sign Up Operation
router.post('/login', commonController.LoginOperations);
router.post('/register', commonController.SignUpOperation);

// ChannelConnectorAPIs
router.post('/api/job', auth.checkMultiAuthorization, commonController.performCrudOperationForJobs);

// Api for binning system
router.post('/api/assign-bin', auth.checkMultiAuthorization, commonController.assignBinNumber);
router.get('/api/get_bin', auth.checkMultiAuthorization, commonController.getBinDetails);
router.post('/api/mark-order-complete', auth.checkMultiAuthorization, commonController.markOrderComplete);

// API endpoint create and update channels
router.post('/api/channel_details', auth.checkMultiAuthorization, commonController.getChannelDetails);
router.post('/api/update_channel_status', auth.checkMultiAuthorization, commonController.updateChannelStatus);

// API for get product
router.post('/common/api/get_product', auth.checkMultiAuthorization, commonController.getProduct)
// router.post('/common/api/get_product_varient', auth.checkMultiAuthorization, commonController.getvarient)
router.post('/common/api/get_all_product_varient', auth.checkMultiAuthorization, commonController.getAllVarient)

// API to get Orders
router.post('/common/api/get_order_master', auth.checkMultiAuthorization, commonController.getOrderMasterDetails)
router.post('/common/api/get_order_detail', auth.checkMultiAuthorization, commonController.getOrderDetails)

// APIs for create Order
router.post('/common/api/create_shopify_order', auth.checkMultiAuthorization, commonController.createShopifyOrder)
router.post('/common/api/create_order_with_inventory', auth.checkMultiAuthorization, commonController.createOrderWithInventoryTracking)

// APIs for Create Product
// router.post('/common/api/push_shopify_product_in_bulk', auth.checkMultiAuthorization, commonController.pushShopifyProductInBulk)
router.post('/common/api/push_shopify_product_varient_in_bulk', auth.checkMultiAuthorization, commonController.pushShopifyProductVarientInBulk)
router.post('/common/api/push_shopify_product', auth.checkMultiAuthorization, commonController.pushShopifyProduct)

// router.post('/common/api/update_shopify_product_with_varient', auth.checkMultiAuthorization, commonController.updateShopifyProductwithVarient)
// router.post('/common/api/update_shopify_varient', auth.checkMultiAuthorization, commonController.updateVarient)

// API to delete product
router.post('/common/api/delete_shopify_product', auth.checkMultiAuthorization, commonController.deleteShopifyProduct)

// router.post('/common/api/update_pricing', auth.checkMultiAuthorization, commonController.updatePricing)
// router.post('/common/api/update_inventary', auth.checkMultiAuthorization, commonController.updateShopifyInventoryInBulk)

// API to Add Tracking
router.post('/common/api/push_tracking', auth.checkMultiAuthorization, commonController.pushTrackingInBulk)

/**
 * login and auth rquest
 * auth now required
 */
router.post('/api/auth/signup', auth.authenticateToken, auth.checkPermission(), authController.signup)
router.post('/api/auth/token', authController.login)

/**
 * with Auth Request
 */
// API to get Orders
router.get('/api/cc/get_order_master', commonController.getOrderMasterDetails)
router.get('/api/cc/get_order_detail', auth.authenticateToken, commonController.getOrderDetails)


/** 
 * API for auth users 
 */
// Get all user by group id
router.get('/api/auth/users', auth.authenticateToken, auth.checkPermission(), authController.getAllAuthUsers)
// Get user by id
router.get('/api/auth/users/:id', auth.authenticateToken, auth.checkPermission(), authController.getAuthtUserById)
// updated user 
router.put('/api/auth/users/:id', auth.authenticateToken, auth.checkPermission(), authController.editAuthUser)
// delete user 
router.delete('/api/auth/users/:id', auth.authenticateToken, auth.checkPermission(), authController.deleteAuthUser)
//change password
router.post('/api/auth/user/change-password', auth.authenticateToken, auth.checkPermission(), authController.changePassword);

//get all user roles Permission and roles

router.get('/api/roles', auth.authenticateToken, auth.checkPermission(), roleController.getRoles)

// API for dashboard

router.get('/api/dashboard', auth.authenticateToken, auth.checkPermission(), dashboardController.getDashboardData);

// orders APIs
router.post("/api/bulk-orders/csv", auth.authenticateToken, upload.orderUpload.single("file"), hclOrderController.uploadBulkOrders);
router.post('/api/order', auth.authenticateToken, hclOrderController.createOrder)
router.get('/api/order', auth.authenticateToken, hclOrderController.getOrderById)
router.put('/api/order/:orderId', auth.authenticateToken, hclOrderController.updateOrder)
// for updating order status
router.put('/api/order/update/status', auth.authenticateToken, hclOrderController.orderUpdateStatus)
// for deleting order 
router.delete('/api/order/:orderId', auth.authenticateToken, hclOrderController.deleteOrder)

//get order by multiple filters 
router.get('/api/order/get_all_orders', auth.authenticateToken, auth.checkPermission(), hclOrderController.getAllOrders)
router.post('/api/order/search_orders', auth.authenticateToken, auth.checkPermission(), hclOrderController.searchOrders)

// order ship 
router.post('/api/ship-order', auth.authenticateToken, auth.checkPermission(), shipmentsController.createOrderShipment)
router.get('/api/shipment-cost', auth.authenticateToken, auth.checkPermission(), shipmentsController.getShipmentCost)
router.post('/api/shipment-cost', auth.authenticateToken, auth.checkPermission(), shipmentsController.getMultiPackageShipmentCost)
router.get('/api/track-shipment', auth.authenticateToken, auth.checkPermission(), shipmentsController.trackShipment)
router.put('/api/cancel-awb', auth.authenticateToken, auth.checkPermission(), shipmentsController.cancelAwb)

// manifest endpoints
router.get('/api/manifests', auth.authenticateToken, auth.checkPermission(), manifestController.getManifestList)
router.get('/api/manifests/:manifestId', auth.authenticateToken, auth.checkPermission(), manifestController.getManifestDetails)
router.patch('/api/manifests/:manifestId/status', auth.authenticateToken, auth.checkPermission(), manifestController.updateManifestStatus)
router.post('/api/manifests', auth.authenticateToken, auth.checkPermission(), manifestController.createManifest)

// Route for creating a new warehouse address
router.post('/api/warehouse', auth.authenticateToken, auth.checkPermission(), warehouseController.createWarehouseAddress)
router.put('/api/warehouse/:warehouseId', auth.authenticateToken, auth.checkPermission(), warehouseController.updateWarehouseAddress)
router.get('/api/warehouse', auth.authenticateToken, auth.checkPermission(), warehouseController.getAllWarehouseAddressesWithPaging)


// COD Payout Entry
router.post('/api/cod-payout', auth.authenticateToken, auth.checkPermission(), codPayoutController.createCodPayoutEntry)
// COD Payout Listing
router.get('/api/cod-payouts', auth.authenticateToken, auth.checkPermission(), codPayoutController.listCodPayouts)
// Log COD Payout
router.post('/api/log-cod-payouts', auth.authenticateToken, auth.checkPermission(), codPayoutController.logCodPayout)
// List COD Payout History
router.get('/api/cod-payout-history', auth.authenticateToken, auth.checkPermission(), codPayoutController.listCodPayoutHistory)
// get cod payout sums
router.get('/api/cod-payout-sums', auth.authenticateToken, auth.checkPermission(), codPayoutController.getCodPayoutSums)


/**
 * Admin Routes
 * for the admin APP
 */

/** API for organizations */

router.post('/api/organization',  auth.authenticateToken, auth.checkPermission(), upload.orgLogoUpload.single('logo'), organizationController.createOrganization)
router.get('/api/organization/:id',  auth.authenticateToken, auth.checkPermission(), organizationController.getOrganization)
router.post('/api/organization/:orgId/details', auth.authenticateToken, auth.checkPermission(), organizationController.createOrgDetails)
router.put('/api/organization/:orgId/pages/:pageType', auth.authenticateToken, auth.checkPermission(), organizationController.updateOrgPage)
router.get('/api/organization/:orgId/pages/:pageType', auth.authenticateToken, auth.checkPermission(), organizationController.getOrgPage)
router.get('/api/organizations',  auth.authenticateToken, auth.checkPermission(), organizationController.getAllOrganizations)

/** 
 * Client route
 * Register, get, getbyId
 * edit, delete, 
*/
router.post('/api/clients', auth.authenticateToken, auth.checkPermission(), clientController.registerClient)
router.get('/api/clients', auth.authenticateToken, auth.checkPermission(), clientController.getAllClients)
router.get('/api/clients/:id', auth.authenticateToken, auth.checkPermission(), clientController.getClientById)
router.put('/api/clients/:id', auth.authenticateToken, auth.checkPermission(), clientController.editClient)
router.delete('/api/clients/:clientId', auth.authenticateToken, auth.checkPermission(), clientController.deleteClientById)

/** 
 * Client Billing Routes
 * Billing info add and get
 *  
*/
router.post('/api/clients/billing-info/upsert', auth.authenticateToken, auth.checkPermission(), clientController.upsertBillingInfo)
router.get('/api/clients/billing-info/:clientId', auth.authenticateToken, auth.checkPermission(), clientController.getBillingInfo)

/** 
 * Wallet Routes for client
 *  
*/
 router.post('/api/wallet/recharge', auth.authenticateToken, auth.checkPermission(), walletController.rechargeWallet)
 router.post('/api/wallet/transaction', auth.authenticateToken, auth.checkPermission(), walletController.walletTransaction)
 // /** get the wallet by client id *  */    
 router.get('/api/wallet/balance/:clientId', auth.authenticateToken, auth.checkPermission(), walletController.getWalletBalancebyClientId)
// /** get the all transactions or by client id *  */    
 router.get('/api/wallet/transactions', auth.authenticateToken, auth.checkPermission(), walletController.getWalletTransactions)
/** get wallet balance clints */
 router.get('/api/wallet/balances', auth.authenticateToken, auth.checkPermission(), walletController.getWalletBalances)
/**
 * pincode APIS
 */

//check pincode serviceability
router.get('/api/pincodes/check-serviceability', auth.authenticateToken, auth.checkPermission(), pincodeController.checkPincodeServiceability)
// get pinocde details
router.get('/api/pincode-with-providers', auth.authenticateToken,auth.checkPermission(),  pincodeController.getPincodeDetailsWithProviders)
// add or update pincode
router.post('/api/pincode/import', auth.authenticateToken, auth.checkPermission(), pincodeController.upload.single('file'), pincodeController.uploadPincodes);
// get pinocde by service providers
router.get('/api/pincodes', auth.authenticateToken, auth.checkPermission(), pincodeController.getShippingPincodes)
// get pinocde details
router.get('/api/pincode', auth.authenticateToken, auth.checkPermission(), pincodeController.getPincodeDetails)
router.post('/api/pincode', auth.authenticateToken, auth.checkPermission(), pincodeController.addPincode)

// search pincode master
router.get('/api/pincode-master', auth.authenticateToken, auth.checkPermission(), pincodeController.searchPincodeMaster)

/**
* Shipping Providers 
*/
//client shipping providers priorities
router.post('/api/add-bulk-priorities', auth.authenticateToken, auth.checkPermission(), shippingProviderController.addBulkPriorities)
router.post('/api/add-priorities', auth.authenticateToken, auth.checkPermission(), shippingProviderController.addPriorities)
router.get('/api/get-priorities', auth.authenticateToken, auth.checkPermission(), shippingProviderController.getPriorities)

// Client Insert/Update Weight Slab
router.post('/api/weight-slab',  auth.authenticateToken, auth.checkPermission(), weightSlabController.upsertWeightSlab)
// Client Get Weight Slabs by Client ID
router.get('/api/weight-slabs',  auth.authenticateToken,auth.checkPermission(), weightSlabController.getWeightSlabs)
//Client zone wise rates
router.get('/api/client-zones-rates/', auth.authenticateToken, auth.checkPermission(), clientsRatesController.getClientZonesRates)
// Client Upsert clients zones rates
router.post('/api/client-zones-rates/upsert', auth.authenticateToken, auth.checkPermission(), clientsRatesController.upsertClientZonesRates)
// Client get and upsert clients additiona rates
router.get('/api/client-rate/additional-rates', auth.authenticateToken, auth.checkPermission(), clientsRatesController.getClientAdditionalRates)
router.post('/api/client-rate/additional-rates', auth.authenticateToken, auth.checkPermission(), clientsRatesController.upsertClientAdditionalRates)
//client shipping providers
router.get('/api/client/providers', auth.authenticateToken, auth.checkPermission(), clientsRatesController.getClientProviders)
router.get('/api/client/all-enabled-providers', auth.authenticateToken, auth.checkPermission(), clientsRatesController.getClientAllEnabledProviders)
router.post('/api/client/providers', auth.authenticateToken, auth.checkPermission(), clientsRatesController.upsertClientProvider)

// Client Route to get COD Rate
router.get('/api/cod-rates', auth.authenticateToken, auth.checkPermission(), clientsRatesController.getCodRateByClient)
// Client Route to upsert COD Rate
router.post('/api/cod-rates/upsert', auth.authenticateToken, auth.checkPermission(), clientsRatesController.upsertClientCODRates)
//shipping providers

router.get('/api/shipping-provider/:providerId', auth.authenticateToken, auth.checkPermission(), shippingProviderController.getShippingProviderDeatils)
router.get('/api/shipping-providers', auth.authenticateToken, auth.checkPermission(), shippingProviderController.getShippingProviders)
router.post('/api/shipping-provider', auth.authenticateToken, auth.checkPermission(), shippingProviderController.addShippingProvider)
router.put('/api/shipping-provider/:id', auth.authenticateToken, auth.checkPermission(), shippingProviderController.updateShippingProvider)
// shipping providers weights
router.post('/api/providers/weights', auth.authenticateToken, auth.checkPermission(), shippingProviderWeightsController.upsertWeightSlab)
router.get('/api/providers/weights', auth.authenticateToken, auth.checkPermission(), shippingProviderWeightsController.getWeightSlabs)
// shipping providers rates
router.post('/api/providers/rates', auth.authenticateToken, auth.checkPermission(), shippingProviderRatesController.upsertShippingProviderRates)
router.get('/api/providers/rates', auth.authenticateToken, auth.checkPermission(), shippingProviderRatesController.getShippingProviderRates)
router.get('/api/providers/zone-rates', auth.authenticateToken, auth.checkPermission(), shippingProviderRatesController.getShippingProviderZoneRates)
router.post('/api/providers/additional-rates', auth.authenticateToken, auth.checkPermission(), shippingProviderRatesController.upsertShippingProviderAdditionalRates)
router.get('/api/providers/additional-rates', auth.authenticateToken, auth.checkPermission(), shippingProviderRatesController.getShippingProviderAdditionalRates)

/**Add/Update Zone */
router.get('/api/zone/delivery-days', auth.authenticateToken, auth.checkPermission(), zoneController.getDeliveryDays)
router.post('/api/zone/delivery-days', auth.authenticateToken, auth.checkPermission(), zoneController.upsertDeliveryDays)
router.get('/api/zone/optimal-delivery', auth.authenticateToken, auth.checkPermission(), zoneController.getOptimalDelivery)
router.post('/api/zone/clear-cache', auth.authenticateToken, auth.checkPermission(), zoneController.clearCache)
// upsert zones and get zones
router.post('/api/zone', auth.authenticateToken, auth.checkPermission(), zoneController.upsertZone)
router.get('/api/zones', auth.authenticateToken, auth.checkPermission(), zoneController.getZones)
router.get('/api/zone/:id', auth.authenticateToken, auth.checkPermission(), zoneController.getZoneById)

// payment gateway
router.get('/api/payment', auth.authenticateToken, auth.checkPermission(), paymentController.getPaymentMethods)

// API to status Orders
router.get('/api/statuses-master', auth.authenticateToken, auth.checkPermission(), statusController.getStatusesMaster)
router.get('/api/states-master', auth.authenticateToken, auth.checkPermission(), statusController.getStatesMaster)
router.get('/api/data-masters', auth.authenticateToken, auth.checkPermission(), statusController.getDataMasters)
router.get('/api/statuses', auth.authenticateToken, auth.checkPermission(), statusController.getAllStatuses)

router.post('/api/order/:orderId/trackings', auth.authenticateToken, auth.checkPermission(), hclOrderController.addTrackingEvents);


module.exports = router;