import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import axios from 'axios';
import { AddNewOrder } from '../pages/Order/Addorder/AddNewOrder';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  clear: jest.fn()
};
global.localStorage = mockLocalStorage;

// Mock environment variables
process.env.REACT_APP_BASE_URL = 'http://localhost:3001';
process.env.REACT_APP_ACCESS_TOKEN = 'test-token';

// Mock components
jest.mock('../components/Navbar/Navbar', () => ({
  NavBar: () => <div data-testid="navbar">NavBar</div>
}));

jest.mock('../components/SidePanel/Sidebar', () => ({
  Sidebar: () => <div data-testid="sidebar">Sidebar</div>
}));

const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('AddNewOrder Component', () => {
  beforeEach(() => {
    mockLocalStorage.getItem.mockReturnValue('1002');
    mockedAxios.post.mockClear();
  });

  describe('Product Browse Functionality', () => {
    const mockProducts = [
      {
        id: 1,
        title: 'Test Product 1',
        sku: 'TEST-001',
        price: 10.00,
        inventory_quantity: 5,
        image: 'test-image.jpg',
        skuFamily: 'TEST',
        option_1_value: 'Red',
        option_2_value: 'Large'
      },
      {
        id: 2,
        title: 'Test Product 1',
        sku: 'TEST-002',
        price: 12.00,
        inventory_quantity: 3,
        image: 'test-image2.jpg',
        skuFamily: 'TEST',
        option_1_value: 'Blue',
        option_2_value: 'Medium'
      },
      {
        id: 3,
        title: 'Another Product',
        sku: 'OTHER-001',
        price: 15.00,
        inventory_quantity: 0,
        image: 'other-image.jpg',
        skuFamily: 'OTHER'
      }
    ];

    test('should fetch and display products when browse button is clicked', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: {
            data: mockProducts
          }
        }
      });

      renderWithRouter(<AddNewOrder />);
      
      const browseButton = screen.getByText('Browse');
      fireEvent.click(browseButton);

      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          'http://localhost:3001/common/api/get_all_product_varient?fby_user_id=1002',
          {},
          expect.objectContaining({
            headers: expect.objectContaining({
              'Content-Type': 'application/json',
              Authorization: 'Bearer test-token '
            })
          })
        );
      });

      await waitFor(() => {
        expect(screen.getByText('Select Products')).toBeInTheDocument();
      });
    });

    test('should group products by base name', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: {
            data: mockProducts
          }
        }
      });

      renderWithRouter(<AddNewOrder />);
      
      const browseButton = screen.getByText('Browse');
      fireEvent.click(browseButton);

      await waitFor(() => {
        expect(screen.getByText('TEST')).toBeInTheDocument();
        expect(screen.getByText('OTHER')).toBeInTheDocument();
        expect(screen.getByText('2 variants')).toBeInTheDocument();
        expect(screen.getByText('1 variant')).toBeInTheDocument();
      });
    });

    test('should show inventory status correctly', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: {
            data: mockProducts
          }
        }
      });

      renderWithRouter(<AddNewOrder />);
      
      const browseButton = screen.getByText('Browse');
      fireEvent.click(browseButton);

      await waitFor(() => {
        expect(screen.getByText('5 available')).toBeInTheDocument();
        expect(screen.getByText('3 available')).toBeInTheDocument();
        expect(screen.getByText('0 available')).toBeInTheDocument();
      });
    });
  });

  describe('Quantity Selection', () => {
    test('should allow quantity input and validate against inventory', async () => {
      const mockProducts = [
        {
          id: 1,
          title: 'Test Product',
          sku: 'TEST-001',
          price: 10.00,
          inventory_quantity: 5,
          skuFamily: 'TEST'
        }
      ];

      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: {
            data: mockProducts
          }
        }
      });

      renderWithRouter(<AddNewOrder />);
      
      const browseButton = screen.getByText('Browse');
      fireEvent.click(browseButton);

      await waitFor(() => {
        const quantityInput = screen.getByDisplayValue('0');
        fireEvent.change(quantityInput, { target: { value: '3' } });
        expect(quantityInput.value).toBe('3');
      });
    });

    test('should prevent quantity exceeding inventory', async () => {
      const mockProducts = [
        {
          id: 1,
          title: 'Test Product',
          sku: 'TEST-001',
          price: 10.00,
          inventory_quantity: 2,
          skuFamily: 'TEST'
        }
      ];

      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: {
            data: mockProducts
          }
        }
      });

      renderWithRouter(<AddNewOrder />);
      
      const browseButton = screen.getByText('Browse');
      fireEvent.click(browseButton);

      await waitFor(() => {
        const quantityInput = screen.getByDisplayValue('0');
        fireEvent.change(quantityInput, { target: { value: '5' } });
        
        // Should show error message
        expect(screen.getByText(/Maximum available quantity/)).toBeInTheDocument();
      });
    });
  });

  describe('Order Creation', () => {
    test('should create order with inventory tracking', async () => {
      const mockProducts = [
        {
          id: 1,
          title: 'Test Product',
          sku: 'TEST-001',
          price: 10.00,
          inventory_quantity: 5,
          skuFamily: 'TEST'
        }
      ];

      // Mock product fetch
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: {
            data: mockProducts
          }
        }
      });

      // Mock order creation
      mockedAxios.mockResolvedValueOnce({
        data: {
          success: true,
          message: 'Order created successfully'
        }
      });

      renderWithRouter(<AddNewOrder />);
      
      // Add product to order
      const browseButton = screen.getByText('Browse');
      fireEvent.click(browseButton);

      await waitFor(() => {
        const quantityInput = screen.getByDisplayValue('0');
        fireEvent.change(quantityInput, { target: { value: '2' } });
        
        const addToOrderButton = screen.getByText('Add to Order');
        fireEvent.click(addToOrderButton);
      });

      // Save order
      const saveButton = screen.getByText('Save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockedAxios).toHaveBeenCalledWith(
          expect.objectContaining({
            url: 'http://localhost:3001/common/api/create_order_with_inventory/?fby_user_id=1002',
            method: 'POST',
            data: expect.objectContaining({
              line_items: expect.arrayContaining([
                expect.objectContaining({
                  variant_id: 1,
                  quantity: 2,
                  sku: 'TEST-001',
                  price: 10.00
                })
              ])
            })
          })
        );
      });
    });

    test('should handle insufficient inventory error', async () => {
      const mockProducts = [
        {
          id: 1,
          title: 'Test Product',
          sku: 'TEST-001',
          price: 10.00,
          inventory_quantity: 1,
          skuFamily: 'TEST'
        }
      ];

      // Mock product fetch
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: {
            data: mockProducts
          }
        }
      });

      // Mock order creation failure
      mockedAxios.mockRejectedValueOnce({
        response: {
          data: {
            error: true,
            message: 'Insufficient inventory'
          }
        }
      });

      renderWithRouter(<AddNewOrder />);
      
      // Try to add more than available
      const browseButton = screen.getByText('Browse');
      fireEvent.click(browseButton);

      await waitFor(() => {
        const quantityInput = screen.getByDisplayValue('0');
        fireEvent.change(quantityInput, { target: { value: '5' } });
        
        const addToOrderButton = screen.getByText('Add to Order');
        fireEvent.click(addToOrderButton);
      });

      // Try to save order
      const saveButton = screen.getByText('Save');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText(/Some products have insufficient inventory/)).toBeInTheDocument();
      });
    });
  });

  describe('Order Summary', () => {
    test('should display order summary with correct totals', async () => {
      const mockProducts = [
        {
          id: 1,
          title: 'Test Product 1',
          sku: 'TEST-001',
          price: 10.00,
          inventory_quantity: 5,
          skuFamily: 'TEST'
        },
        {
          id: 2,
          title: 'Test Product 2',
          sku: 'TEST-002',
          price: 15.00,
          inventory_quantity: 3,
          skuFamily: 'TEST2'
        }
      ];

      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: {
            data: mockProducts
          }
        }
      });

      renderWithRouter(<AddNewOrder />);
      
      const browseButton = screen.getByText('Browse');
      fireEvent.click(browseButton);

      await waitFor(() => {
        // Add first product
        const quantityInputs = screen.getAllByDisplayValue('0');
        fireEvent.change(quantityInputs[0], { target: { value: '2' } });
        fireEvent.change(quantityInputs[1], { target: { value: '1' } });
        
        const addToOrderButton = screen.getByText('Add to Order');
        fireEvent.click(addToOrderButton);
      });

      await waitFor(() => {
        expect(screen.getByText('Total Items: 3')).toBeInTheDocument();
        expect(screen.getByText('Total Value: $35.00')).toBeInTheDocument();
      });
    });
  });
});
