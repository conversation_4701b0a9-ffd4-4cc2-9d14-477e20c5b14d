{"ast": null, "code": "// api/products.js\nimport axios from 'axios';\nexport const getProducts = () => {\n  return new Promise((resolve, reject) => {\n    var storedGroupCode = localStorage.getItem(\"groupCode\");\n    setTimeout(async () => {\n      try {\n        const res = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_product?fby_user_id=${storedGroupCode}`, {}, {\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n          }\n        });\n        if (res.data.success.data.length > 0) {\n          const responseData = res.data.success.data.map(element => ({\n            id: element.id,\n            title: element.title,\n            description: element.description,\n            option_1_name: element.option_1_name,\n            option_1_value: element.option2_value,\n            option_2_name: element.option_2_name,\n            option_2_value: element.option_2_value,\n            sku: element.sku,\n            gtin: element.gtin,\n            asin: element.asin,\n            quantity: element.inventory_quantity,\n            price: element.price,\n            image_link: element.image,\n            brand: element.brand,\n            tags: element.tags,\n            category: element.category,\n            weight: element.weight,\n            weight_unit: element.weight_unit,\n            height: element.height,\n            width: element.width,\n            length: element.length,\n            dimensions_units: element.dimensions_units,\n            status: element.product_status,\n            variantPrice: element.variantPrice,\n            quantity: element.quantity,\n            inventory_quantity: element.inventory_quantity,\n            count: element.count,\n            channel_status: element.channel_status,\n            created_at: element.created_at,\n            updated_at: element.updated_at\n          }));\n          resolve(responseData);\n        }\n      } catch (err) {\n        reject(err.message);\n      }\n    });\n  });\n};\nexport const getExportProducts = () => {\n  var storedGroupCode = localStorage.getItem(\"groupCode\");\n  return new Promise((resolve, reject) => {\n    setTimeout(async () => {\n      try {\n        const res = await axios.post(`${process.env.REACT_APP_BASE_URL}/common/api/get_all_product_varient?fby_user_id=${storedGroupCode}`, {}, {\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\n          }\n        });\n        if (res.data.success.data.length > 0) {\n          const responseData = res.data.success.data.map(element => ({\n            id: element.id,\n            title: element.title,\n            description: element.description,\n            option_1_name: element.option_1_name,\n            option_1_value: element.option2_value,\n            option_2_name: element.option_2_name,\n            option_2_value: element.option_2_value,\n            sku: element.sku,\n            gtin: element.gtin,\n            asin: element.asin,\n            quantity: element.inventory_quantity,\n            price: element.price,\n            image_link: element.image,\n            brand: element.brand,\n            tags: element.tags,\n            category: element.category,\n            weight: element.weight_value,\n            weight_unit: element.weight_unit,\n            height: element.dimensions_height,\n            width: element.dimensions_width,\n            length: element.dimensions_length,\n            dimensions_units: element.dimensions_units\n          }));\n          resolve(responseData);\n        }\n      } catch (err) {\n        reject(err.message);\n      }\n    });\n  });\n};", "map": {"version": 3, "names": ["axios", "getProducts", "Promise", "resolve", "reject", "storedGroupCode", "localStorage", "getItem", "setTimeout", "res", "post", "process", "env", "REACT_APP_BASE_URL", "headers", "Authorization", "REACT_APP_ACCESS_TOKEN", "data", "success", "length", "responseData", "map", "element", "id", "title", "description", "option_1_name", "option_1_value", "option2_value", "option_2_name", "option_2_value", "sku", "gtin", "asin", "quantity", "inventory_quantity", "price", "image_link", "image", "brand", "tags", "category", "weight", "weight_unit", "height", "width", "dimensions_units", "status", "product_status", "variantPrice", "count", "channel_status", "created_at", "updated_at", "err", "message", "getExportProducts", "weight_value", "dimensions_height", "dimensions_width", "dimensions_length"], "sources": ["E:/digispin doc/repos/logistics/DigiConnector/digiconnectorfrontend/src/api/products.jsx"], "sourcesContent": ["// api/products.js\r\nimport axios from 'axios';\r\n\r\nexport const getProducts = () => {\r\n  return new Promise((resolve, reject) => {\r\n    var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n    setTimeout(async () => {\r\n      try {\r\n        const res = await axios.post(\r\n          `${process.env.REACT_APP_BASE_URL}/common/api/get_product?fby_user_id=${storedGroupCode}`,\r\n          {},\r\n          {\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n              Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n            }\r\n          }\r\n        );\r\n        if (res.data.success.data.length > 0) {\r\n          const responseData = res.data.success.data.map(element => ({\r\n            id: element.id,\r\n            title: element.title,\r\n            description: element.description,\r\n            option_1_name: element.option_1_name,\r\n            option_1_value: element.option2_value,\r\n            option_2_name: element.option_2_name,\r\n            option_2_value: element.option_2_value,\r\n            sku: element.sku,\r\n            gtin: element.gtin,\r\n            asin: element.asin,\r\n            quantity: element.inventory_quantity,\r\n            price: element.price,\r\n            image_link: element.image,\r\n            brand: element.brand,\r\n            tags: element.tags,\r\n            category: element.category,\r\n            weight: element.weight,\r\n            weight_unit: element.weight_unit,\r\n            height: element.height,\r\n            width: element.width,\r\n            length: element.length,\r\n            dimensions_units: element.dimensions_units,\r\n            status: element.product_status,\r\n            variantPrice: element.variantPrice,\r\n            quantity: element.quantity,\r\n            inventory_quantity: element.inventory_quantity,\r\n            count: element.count,\r\n            channel_status: element.channel_status,\r\n            created_at: element.created_at,\r\n            updated_at: element.updated_at\r\n          }));\r\n          resolve(responseData);\r\n        }\r\n      } catch (err) {\r\n        reject(err.message);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\nexport const getExportProducts = () => {\r\n  var storedGroupCode = localStorage.getItem(\"groupCode\");\r\n\r\n  return new Promise((resolve, reject) => {\r\n    setTimeout(async () => {\r\n      try {\r\n        const res = await axios.post(\r\n          `${process.env.REACT_APP_BASE_URL}/common/api/get_all_product_varient?fby_user_id=${storedGroupCode}`,\r\n          {},\r\n          {\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n              Authorization: `Bearer ${process.env.REACT_APP_ACCESS_TOKEN}`\r\n            }\r\n          }\r\n        );\r\n        if (res.data.success.data.length > 0) {\r\n          const responseData = res.data.success.data.map(element => ({\r\n            id: element.id,\r\n            title: element.title,\r\n            description: element.description,\r\n            option_1_name: element.option_1_name,\r\n            option_1_value: element.option2_value,\r\n            option_2_name: element.option_2_name,\r\n            option_2_value: element.option_2_value,\r\n            sku: element.sku,\r\n            gtin: element.gtin,\r\n            asin: element.asin,\r\n            quantity: element.inventory_quantity,\r\n            price: element.price,\r\n            image_link: element.image,\r\n            brand: element.brand,\r\n            tags: element.tags,\r\n            category: element.category,\r\n            weight: element.weight_value,\r\n            weight_unit: element.weight_unit,\r\n            height: element.dimensions_height,\r\n            width: element.dimensions_width,\r\n            length: element.dimensions_length,\r\n            dimensions_units: element.dimensions_units\r\n          }));\r\n          resolve(responseData);\r\n        }\r\n      } catch (err) {\r\n        reject(err.message);\r\n      }\r\n    });\r\n  });\r\n};\r\n"], "mappings": "AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC/B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAIC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACvDC,UAAU,CAAC,YAAY;MACrB,IAAI;QACF,MAAMC,GAAG,GAAG,MAAMT,KAAK,CAACU,IAAI,CAC1B,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,uCAAuCR,eAAe,EAAE,EACzF,CAAC,CAAC,EACF;UACES,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAUJ,OAAO,CAACC,GAAG,CAACI,sBAAsB;UAC7D;QACF,CACF,CAAC;QACD,IAAIP,GAAG,CAACQ,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE;UACpC,MAAMC,YAAY,GAAGX,GAAG,CAACQ,IAAI,CAACC,OAAO,CAACD,IAAI,CAACI,GAAG,CAACC,OAAO,KAAK;YACzDC,EAAE,EAAED,OAAO,CAACC,EAAE;YACdC,KAAK,EAAEF,OAAO,CAACE,KAAK;YACpBC,WAAW,EAAEH,OAAO,CAACG,WAAW;YAChCC,aAAa,EAAEJ,OAAO,CAACI,aAAa;YACpCC,cAAc,EAAEL,OAAO,CAACM,aAAa;YACrCC,aAAa,EAAEP,OAAO,CAACO,aAAa;YACpCC,cAAc,EAAER,OAAO,CAACQ,cAAc;YACtCC,GAAG,EAAET,OAAO,CAACS,GAAG;YAChBC,IAAI,EAAEV,OAAO,CAACU,IAAI;YAClBC,IAAI,EAAEX,OAAO,CAACW,IAAI;YAClBC,QAAQ,EAAEZ,OAAO,CAACa,kBAAkB;YACpCC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACgB,KAAK;YACzBC,KAAK,EAAEjB,OAAO,CAACiB,KAAK;YACpBC,IAAI,EAAElB,OAAO,CAACkB,IAAI;YAClBC,QAAQ,EAAEnB,OAAO,CAACmB,QAAQ;YAC1BC,MAAM,EAAEpB,OAAO,CAACoB,MAAM;YACtBC,WAAW,EAAErB,OAAO,CAACqB,WAAW;YAChCC,MAAM,EAAEtB,OAAO,CAACsB,MAAM;YACtBC,KAAK,EAAEvB,OAAO,CAACuB,KAAK;YACpB1B,MAAM,EAAEG,OAAO,CAACH,MAAM;YACtB2B,gBAAgB,EAAExB,OAAO,CAACwB,gBAAgB;YAC1CC,MAAM,EAAEzB,OAAO,CAAC0B,cAAc;YAC9BC,YAAY,EAAE3B,OAAO,CAAC2B,YAAY;YAClCf,QAAQ,EAAEZ,OAAO,CAACY,QAAQ;YAC1BC,kBAAkB,EAAEb,OAAO,CAACa,kBAAkB;YAC9Ce,KAAK,EAAE5B,OAAO,CAAC4B,KAAK;YACpBC,cAAc,EAAE7B,OAAO,CAAC6B,cAAc;YACtCC,UAAU,EAAE9B,OAAO,CAAC8B,UAAU;YAC9BC,UAAU,EAAE/B,OAAO,CAAC+B;UACtB,CAAC,CAAC,CAAC;UACHlD,OAAO,CAACiB,YAAY,CAAC;QACvB;MACF,CAAC,CAAC,OAAOkC,GAAG,EAAE;QACZlD,MAAM,CAACkD,GAAG,CAACC,OAAO,CAAC;MACrB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EACrC,IAAInD,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAEvD,OAAO,IAAIL,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtCI,UAAU,CAAC,YAAY;MACrB,IAAI;QACF,MAAMC,GAAG,GAAG,MAAMT,KAAK,CAACU,IAAI,CAC1B,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,mDAAmDR,eAAe,EAAE,EACrG,CAAC,CAAC,EACF;UACES,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAUJ,OAAO,CAACC,GAAG,CAACI,sBAAsB;UAC7D;QACF,CACF,CAAC;QACD,IAAIP,GAAG,CAACQ,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE;UACpC,MAAMC,YAAY,GAAGX,GAAG,CAACQ,IAAI,CAACC,OAAO,CAACD,IAAI,CAACI,GAAG,CAACC,OAAO,KAAK;YACzDC,EAAE,EAAED,OAAO,CAACC,EAAE;YACdC,KAAK,EAAEF,OAAO,CAACE,KAAK;YACpBC,WAAW,EAAEH,OAAO,CAACG,WAAW;YAChCC,aAAa,EAAEJ,OAAO,CAACI,aAAa;YACpCC,cAAc,EAAEL,OAAO,CAACM,aAAa;YACrCC,aAAa,EAAEP,OAAO,CAACO,aAAa;YACpCC,cAAc,EAAER,OAAO,CAACQ,cAAc;YACtCC,GAAG,EAAET,OAAO,CAACS,GAAG;YAChBC,IAAI,EAAEV,OAAO,CAACU,IAAI;YAClBC,IAAI,EAAEX,OAAO,CAACW,IAAI;YAClBC,QAAQ,EAAEZ,OAAO,CAACa,kBAAkB;YACpCC,KAAK,EAAEd,OAAO,CAACc,KAAK;YACpBC,UAAU,EAAEf,OAAO,CAACgB,KAAK;YACzBC,KAAK,EAAEjB,OAAO,CAACiB,KAAK;YACpBC,IAAI,EAAElB,OAAO,CAACkB,IAAI;YAClBC,QAAQ,EAAEnB,OAAO,CAACmB,QAAQ;YAC1BC,MAAM,EAAEpB,OAAO,CAACmC,YAAY;YAC5Bd,WAAW,EAAErB,OAAO,CAACqB,WAAW;YAChCC,MAAM,EAAEtB,OAAO,CAACoC,iBAAiB;YACjCb,KAAK,EAAEvB,OAAO,CAACqC,gBAAgB;YAC/BxC,MAAM,EAAEG,OAAO,CAACsC,iBAAiB;YACjCd,gBAAgB,EAAExB,OAAO,CAACwB;UAC5B,CAAC,CAAC,CAAC;UACH3C,OAAO,CAACiB,YAAY,CAAC;QACvB;MACF,CAAC,CAAC,OAAOkC,GAAG,EAAE;QACZlD,MAAM,CAACkD,GAAG,CAACC,OAAO,CAAC;MACrB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}